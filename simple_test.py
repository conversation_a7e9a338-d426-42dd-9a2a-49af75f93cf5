#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的API测试脚本
只测试两个核心接口：插入生产消息和插入故障数据
"""

import requests
import json
from datetime import datetime

# API服务器地址
BASE_URL = "http://localhost:5000"

def test_production_message():
    """测试插入生产消息"""
    print("=== 测试插入生产消息 ===")
    
    url = f"{BASE_URL}/api/production/message"
    data = {
        "topic": "factory/line1/counter",
        "counter": 1
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def test_malfunction_data():
    """测试插入故障数据"""
    print("\n=== 测试插入故障数据 ===")
    
    url = f"{BASE_URL}/api/malfunction/raw"
    data = {
        "line_number": 1,
        "status": "A",
        "cmd": 1
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def test_with_timestamp():
    """测试带时间戳的请求"""
    print("\n=== 测试带时间戳的请求 ===")
    
    current_time = datetime.now().isoformat()
    
    # 测试生产消息
    print("1. 生产消息（带时间戳）:")
    url = f"{BASE_URL}/api/production/message"
    data = {
        "topic": "factory/line1/counter",
        "counter": 100,
        "received_at": current_time
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"   请求失败: {e}")
    
    # 测试故障数据
    print("\n2. 故障数据（带时间戳）:")
    url = f"{BASE_URL}/api/malfunction/raw"
    data = {
        "line_number": 1,
        "status": "B",
        "cmd": 1,
        "timestamp": current_time
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"   请求失败: {e}")

def test_multiple_malfunctions():
    """测试多种故障类型"""
    print("\n=== 测试多种故障类型 ===")
    
    malfunctions = [
        {"status": "A", "name": "假焊"},
        {"status": "B", "name": "连锡"},
        {"status": "C", "name": "错料"},
        {"status": "D", "name": "漏料"},
        {"status": "E", "name": "反装"}
    ]
    
    url = f"{BASE_URL}/api/malfunction/raw"
    
    for mal in malfunctions:
        data = {
            "line_number": 1,
            "status": mal["status"],
            "cmd": 1
        }
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                print(f"✓ {mal['name']}({mal['status']}) 插入成功")
            else:
                print(f"✗ {mal['name']}({mal['status']}) 插入失败: {response.json()}")
        except Exception as e:
            print(f"✗ {mal['name']}({mal['status']}) 请求失败: {e}")

def test_production_sequence():
    """测试生产序列"""
    print("\n=== 测试生产序列 ===")
    
    url = f"{BASE_URL}/api/production/message"
    
    for i in range(1, 6):
        data = {
            "topic": "factory/line1/counter",
            "counter": i
        }
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                print(f"✓ 生产计数 {i} 插入成功")
            else:
                print(f"✗ 生产计数 {i} 插入失败: {response.json()}")
        except Exception as e:
            print(f"✗ 生产计数 {i} 请求失败: {e}")

def test_error_cases():
    """测试错误情况"""
    print("\n=== 测试错误情况 ===")
    
    # 测试缺少必需字段
    print("1. 测试缺少topic字段:")
    try:
        response = requests.post(
            f"{BASE_URL}/api/production/message",
            json={"counter": 1}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"   请求失败: {e}")
    
    # 测试无效的故障状态
    print("\n2. 测试无效的故障状态:")
    try:
        response = requests.post(
            f"{BASE_URL}/api/malfunction/raw",
            json={"line_number": 1, "status": "Z", "cmd": 1}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"   请求失败: {e}")

def check_server():
    """检查服务器是否运行"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主测试函数"""
    print("开始API测试...")
    
    # 检查服务器
    if not check_server():
        print("❌ 服务器未运行，请先启动 flask_api.py")
        print("启动命令: python flask_api.py")
        return
    
    print("✅ 服务器运行正常")
    
    # 运行测试
    test_production_message()
    test_malfunction_data()
    test_with_timestamp()
    test_multiple_malfunctions()
    test_production_sequence()
    test_error_cases()
    
    print("\n🎉 API测试完成！")

if __name__ == "__main__":
    main()
