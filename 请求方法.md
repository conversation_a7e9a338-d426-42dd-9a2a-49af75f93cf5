# API 请求方法

## 启动服务器
```bash
python minimal_api.py
```
**服务器地址：** `http://localhost:5000`

---

## 1. 插入生产消息
**接口：** `POST /api/production/message`

### Python 请求
```python
import requests

url = "http://localhost:5000/api/production/message"
data = {
    "topic": "factory/line1/counter",
    "counter": 1
}

response = requests.post(url, json=data)
print(response.json())
```

### curl 请求
```bash
curl -X POST http://localhost:5000/api/production/message \
  -H "Content-Type: application/json" \
  -d '{"topic": "factory/line1/counter", "counter": 1}'
```

### JavaScript 请求
```javascript
fetch('http://localhost:5000/api/production/message', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    topic: 'factory/line1/counter',
    counter: 1
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

### 请求参数
- `topic` (必需): MQTT主题，如 "factory/line1/counter"
- `counter` (可选): 计数器值
- `received_at` (可选): 接收时间，ISO格式

### 成功响应
```json
{
  "success": true,
  "message_id": 123,
  "topic": "factory/line1/counter",
  "counter": 1,
  "received_at": "2025-08-23T14:30:00.123456"
}
```

---

## 2. 插入故障数据
**接口：** `POST /api/malfunction/raw`

### Python 请求
```python
import requests

url = "http://localhost:5000/api/malfunction/raw"
data = {
    "line_number": 1,
    "status": "A",
    "cmd": 1
}

response = requests.post(url, json=data)
print(response.json())
```

### curl 请求
```bash
curl -X POST http://localhost:5000/api/malfunction/raw \
  -H "Content-Type: application/json" \
  -d '{"line_number": 1, "status": "A", "cmd": 1}'
```

### JavaScript 请求
```javascript
fetch('http://localhost:5000/api/malfunction/raw', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    line_number: 1,
    status: 'A',
    cmd: 1
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

### 请求参数
- `line_number` (必需): 生产线编号，如 1
- `status` (必需): 故障类型，A-J之间的字符
- `cmd` (必需): 操作命令，1=增加，0=减少
- `timestamp` (可选): 记录时间，ISO格式

### 故障类型
- A: 假焊
- B: 连锡
- C: 错料
- D: 漏料
- E: 反装
- F: 坏料
- G: 功能坏
- H: 开路
- I: 短路
- J: 其他

### 成功响应
```json
{
  "success": true,
  "malfunction_id": 456,
  "line_number": 1,
  "status": "A",
  "cmd": 1,
  "timestamp": "2025-08-23T14:30:00.123456"
}
```

---

## 完整示例代码

### Python 完整示例
```python
import requests
import json

# API 基础地址
BASE_URL = "http://localhost:5000"

def insert_production_message(topic, counter=None):
    """插入生产消息"""
    url = f"{BASE_URL}/api/production/message"
    data = {"topic": topic}
    if counter is not None:
        data["counter"] = counter
    
    response = requests.post(url, json=data)
    return response.json()

def insert_malfunction(line_number, status, cmd):
    """插入故障数据"""
    url = f"{BASE_URL}/api/malfunction/raw"
    data = {
        "line_number": line_number,
        "status": status,
        "cmd": cmd
    }
    
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 插入生产消息
    result = insert_production_message("factory/line1/counter", 1)
    print("生产消息:", json.dumps(result, indent=2, ensure_ascii=False))
    
    # 插入故障数据
    result = insert_malfunction(1, "A", 1)
    print("故障数据:", json.dumps(result, indent=2, ensure_ascii=False))
```

### 批量插入示例
```python
import requests

BASE_URL = "http://localhost:5000"

# 批量插入生产消息
for i in range(1, 6):
    data = {"topic": "factory/line1/counter", "counter": i}
    response = requests.post(f"{BASE_URL}/api/production/message", json=data)
    print(f"生产计数 {i}:", response.json())

# 批量插入故障数据
malfunctions = ["A", "B", "C"]
for status in malfunctions:
    data = {"line_number": 1, "status": status, "cmd": 1}
    response = requests.post(f"{BASE_URL}/api/malfunction/raw", json=data)
    print(f"故障 {status}:", response.json())
```

---

## 错误响应
当请求失败时，返回格式：
```json
{
  "success": false,
  "error": "错误描述信息"
}
```

**常见错误：**
- 400: 请求参数错误
- 404: 接口不存在
- 500: 数据库连接失败或服务器内部错误

---

## 快速测试
保存以下代码为 `test.py`：

```python
import requests

# 测试插入生产消息
print("=== 测试生产消息 ===")
response = requests.post(
    "http://localhost:5000/api/production/message",
    json={"topic": "factory/line1/counter", "counter": 1}
)
print(response.json())

# 测试插入故障数据
print("\n=== 测试故障数据 ===")
response = requests.post(
    "http://localhost:5000/api/malfunction/raw",
    json={"line_number": 1, "status": "A", "cmd": 1}
)
print(response.json())
```

运行测试：
```bash
python test.py
```
