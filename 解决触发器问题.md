# 解决触发器问题

## 问题描述
遇到错误：`Can't update table 'line1_production_message_log' in stored function/trigger because it is already used by statement which invoked this stored function/trigger.`

这是因为触发器试图更新同一个正在被插入的表，MySQL不允许这种操作。

## 解决方案

### 方案1：移除触发器（推荐）
执行以下SQL来移除触发器，在API层面处理所有逻辑：

```sql
-- 执行移除触发器脚本
SOURCE remove_triggers.sql;
```

### 方案2：修复触发器
如果您想保留触发器，执行以下SQL：

```sql
-- 执行触发器修复脚本
SOURCE fix_trigger_issue.sql;
```

## 当前API修改
我已经修改了 `production_data_api.py`，现在API会：

1. 在插入消息时直接获取并设置 `product_id`
2. 手动调用 `process_line1_counter` 存储过程处理计数
3. 避免依赖触发器

## 测试修复
执行移除触发器脚本后，重新测试API：

```bash
python quick_test.py
```

## 修改内容

### production_data_api.py 的变化：
- 在插入消息前获取 `product_id`
- 插入时包含 `product_id` 字段
- 手动调用计数处理存储过程
- 返回结果包含 `product_id`

### 数据库变化：
- 移除了会导致冲突的触发器
- 保留了所有存储过程和函数
- 数据完整性通过API层面保证

## 验证步骤

1. **移除触发器**：
   ```sql
   SOURCE remove_triggers.sql;
   ```

2. **测试API**：
   ```bash
   python quick_test.py
   ```

3. **检查数据**：
   ```sql
   -- 检查插入的数据是否包含正确的product_id
   SELECT * FROM line1_production_message_log ORDER BY id DESC LIMIT 5;
   SELECT * FROM line1_production_count;
   SELECT * FROM line1_production_hourly_count ORDER BY id DESC LIMIT 5;
   ```

## 优势
- 避免了触发器的复杂性和限制
- API层面有更好的控制和错误处理
- 更容易调试和维护
- 性能更好（减少了数据库层面的自动处理）
