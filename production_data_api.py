#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产数据插入接口
提供 production 和 malfunction 数据的插入功能
"""

import mysql.connector
from mysql.connector import Error
from datetime import datetime
import json
import logging
from typing import Optional, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProductionDataAPI:
    """生产数据API类"""
    
    def __init__(self, host='localhost', port=3306, user='root', password='', database='production_data'):
        """
        初始化数据库连接
        
        Args:
            host: 数据库主机
            port: 数据库端口
            user: 数据库用户名
            password: 数据库密码
            database: 数据库名称
        """
        self.config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        
    def connect(self) -> bool:
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            if self.connection.is_connected():
                logger.info("数据库连接成功")
                return True
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            return False
        
    def disconnect(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def insert_production_message(self, topic: str, counter: Optional[int] = None,
                                received_at: Optional[datetime] = None) -> Dict[str, Any]:
        """
        插入生产消息记录

        Args:
            topic: MQTT主题
            counter: 计数器值
            received_at: 接收时间，默认为当前时间

        Returns:
            Dict: 操作结果
        """
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}

        try:
            cursor = self.connection.cursor()

            if received_at is None:
                received_at = datetime.now()

            # 获取当前生产线的product_id
            cursor.execute("SELECT product_id FROM line1_production_lines WHERE line_number = 1 LIMIT 1")
            result = cursor.fetchone()
            product_id = result[0] if result else None

            # 插入消息记录，包含product_id
            query = """
                INSERT INTO line1_production_message_log (topic, counter, product_id, received_at)
                VALUES (%s, %s, %s, %s)
            """

            cursor.execute(query, (topic, counter, product_id, received_at))
            message_id = cursor.lastrowid

            # 如果是计数器消息，手动调用处理存储过程
            if topic == 'factory/line1/counter':
                cursor.callproc('process_line1_counter', [topic, received_at])

            cursor.close()

            logger.info(f"生产消息插入成功，ID: {message_id}")
            return {
                "success": True,
                "message_id": message_id,
                "topic": topic,
                "counter": counter,
                "product_id": product_id,
                "received_at": received_at.isoformat()
            }

        except Error as e:
            logger.error(f"插入生产消息失败: {e}")
            return {"success": False, "error": str(e)}
    
    def insert_malfunction_raw(self, line_number: int, status: str, cmd: int,
                             timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        插入故障原始数据
        
        Args:
            line_number: 生产线编号
            status: 故障状态类型 (A-J)
            cmd: 操作命令 (1=增加，0=减少)
            timestamp: 记录时间，默认为当前时间
            
        Returns:
            Dict: 操作结果
        """
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        # 验证输入参数
        if status not in 'ABCDEFGHIJ':
            return {"success": False, "error": "故障状态类型必须是A-J之间的字符"}
        
        if cmd not in [0, 1]:
            return {"success": False, "error": "操作命令必须是0或1"}
        
        try:
            cursor = self.connection.cursor()
            
            if timestamp is None:
                timestamp = datetime.now()
            
            # 调用存储过程插入故障数据
            cursor.callproc('insert_line1_malfunction_raw', 
                          [line_number, status, cmd, timestamp])
            
            # 获取插入的记录ID（需要查询最新插入的记录）
            cursor.execute("""
                SELECT id FROM line1_malfunction_raw_data 
                WHERE line = %s AND status = %s AND cmd = %s 
                ORDER BY id DESC LIMIT 1
            """, (line_number, status, cmd))
            
            result = cursor.fetchone()
            malfunction_id = result[0] if result else None
            
            cursor.close()
            
            logger.info(f"故障数据插入成功，ID: {malfunction_id}")
            return {
                "success": True,
                "malfunction_id": malfunction_id,
                "line_number": line_number,
                "status": status,
                "cmd": cmd,
                "timestamp": timestamp.isoformat()
            }
            
        except Error as e:
            logger.error(f"插入故障数据失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_current_production_count(self) -> Dict[str, Any]:
        """获取当前生产总计数"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute("SELECT * FROM view_line1_current_count")
            result = cursor.fetchone()
            cursor.close()
            
            return {
                "success": True,
                "data": result
            }
            
        except Error as e:
            logger.error(f"获取生产计数失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_today_malfunction_stats(self) -> Dict[str, Any]:
        """获取今日故障统计"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.callproc('get_today_malfunction_stats')
            
            results = []
            for result in cursor.stored_results():
                results.extend(result.fetchall())
            
            cursor.close()
            
            return {
                "success": True,
                "data": results
            }
            
        except Error as e:
            logger.error(f"获取故障统计失败: {e}")
            return {"success": False, "error": str(e)}


def main():
    """测试函数"""
    # 创建API实例
    api = ProductionDataAPI(
        host='localhost',
        user='root',
        password='your_password',  # 请修改为实际密码
        database='production_data'
    )
    
    # 连接数据库
    if not api.connect():
        print("数据库连接失败")
        return
    
    try:
        # 测试插入生产消息
        print("=== 测试插入生产消息 ===")
        result = api.insert_production_message('factory/line1/counter', 1)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 测试插入故障数据
        print("\n=== 测试插入故障数据 ===")
        result = api.insert_malfunction_raw(1, 'A', 1)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 获取当前生产计数
        print("\n=== 获取当前生产计数 ===")
        result = api.get_current_production_count()
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 获取今日故障统计
        print("\n=== 获取今日故障统计 ===")
        result = api.get_today_malfunction_stats()
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    finally:
        # 关闭连接
        api.disconnect()


if __name__ == "__main__":
    main()
