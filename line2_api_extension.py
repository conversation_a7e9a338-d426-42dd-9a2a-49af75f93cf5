#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Line2 API 扩展
为现有 API 添加 line2 支持
"""

from production_data_api import ProductionDataAPI
from datetime import datetime
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class Line2API(ProductionDataAPI):
    """Line2 API 类，继承自 ProductionDataAPI"""
    
    def insert_line2_production_message(self, topic: str, counter: Optional[int] = None, 
                                      received_at: Optional[datetime] = None) -> Dict[str, Any]:
        """
        插入 line2 生产消息记录
        
        Args:
            topic: MQTT主题，如 "factory/line2/counter"
            counter: 计数器值
            received_at: 接收时间，默认为当前时间
            
        Returns:
            Dict: 操作结果
        """
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        try:
            cursor = self.connection.cursor()
            
            if received_at is None:
                received_at = datetime.now()
            
            # 获取当前生产线的product_id
            cursor.execute("SELECT product_id FROM line1_production_lines WHERE line_number = 2 LIMIT 1")
            result = cursor.fetchone()
            product_id = result[0] if result else None
            
            # 插入消息记录，包含product_id
            query = """
                INSERT INTO line2_production_message_log (topic, counter, product_id, received_at)
                VALUES (%s, %s, %s, %s)
            """
            
            cursor.execute(query, (topic, counter, product_id, received_at))
            message_id = cursor.lastrowid
            
            # 如果是计数器消息，手动调用处理存储过程
            if topic == 'factory/line2/counter':
                cursor.callproc('process_line2_counter', [topic, received_at])
            
            cursor.close()
            
            logger.info(f"Line2 生产消息插入成功，ID: {message_id}")
            return {
                "success": True,
                "message_id": message_id,
                "topic": topic,
                "counter": counter,
                "product_id": product_id,
                "received_at": received_at.isoformat(),
                "line": 2
            }
            
        except Exception as e:
            logger.error(f"插入 Line2 生产消息失败: {e}")
            return {"success": False, "error": str(e)}
    
    def insert_line2_malfunction_raw(self, line_number: int, status: str, cmd: int,
                                   timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        插入 line2 故障原始数据
        
        Args:
            line_number: 生产线编号（应该是2）
            status: 故障状态类型 (A-J)
            cmd: 操作命令 (1=增加，0=减少)
            timestamp: 记录时间，默认为当前时间
            
        Returns:
            Dict: 操作结果
        """
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        # 验证输入参数
        if status not in 'ABCDEFGHIJ':
            return {"success": False, "error": "故障状态类型必须是A-J之间的字符"}
        
        if cmd not in [0, 1]:
            return {"success": False, "error": "操作命令必须是0或1"}
        
        if line_number != 2:
            return {"success": False, "error": "Line2 API 只支持生产线编号2"}
        
        try:
            cursor = self.connection.cursor()
            
            if timestamp is None:
                timestamp = datetime.now()
            
            # 调用存储过程插入故障数据
            cursor.callproc('insert_line2_malfunction_raw', 
                          [line_number, status, cmd, timestamp])
            
            # 获取插入的记录ID
            cursor.execute("""
                SELECT id FROM line2_malfunction_raw_data 
                WHERE line = %s AND status = %s AND cmd = %s 
                ORDER BY id DESC LIMIT 1
            """, (line_number, status, cmd))
            
            result = cursor.fetchone()
            malfunction_id = result[0] if result else None
            
            cursor.close()
            
            logger.info(f"Line2 故障数据插入成功，ID: {malfunction_id}")
            return {
                "success": True,
                "malfunction_id": malfunction_id,
                "line_number": line_number,
                "status": status,
                "cmd": cmd,
                "timestamp": timestamp.isoformat(),
                "line": 2
            }
            
        except Exception as e:
            logger.error(f"插入 Line2 故障数据失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_line2_production_count(self) -> Dict[str, Any]:
        """获取 line2 当前生产总计数"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute("SELECT * FROM view_line2_current_count")
            result = cursor.fetchone()
            cursor.close()
            
            return {
                "success": True,
                "data": result,
                "line": 2
            }
            
        except Exception as e:
            logger.error(f"获取 Line2 生产计数失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_line2_today_malfunction_stats(self) -> Dict[str, Any]:
        """获取 line2 今日故障统计"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.callproc('get_line2_today_malfunction_stats')
            
            results = []
            for result in cursor.stored_results():
                results.extend(result.fetchall())
            
            cursor.close()
            
            return {
                "success": True,
                "data": results,
                "line": 2
            }
            
        except Exception as e:
            logger.error(f"获取 Line2 故障统计失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_line2_production_stats(self) -> Dict[str, Any]:
        """获取 line2 生产统计信息"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.callproc('get_line2_production_stats')
            
            results = []
            for result in cursor.stored_results():
                results.extend(result.fetchall())
            
            cursor.close()
            
            return {
                "success": True,
                "data": results,
                "line": 2
            }
            
        except Exception as e:
            logger.error(f"获取 Line2 生产统计失败: {e}")
            return {"success": False, "error": str(e)}


def test_line2_api():
    """测试 Line2 API 功能"""
    # 创建API实例
    api = Line2API(
        host='localhost',
        user='root',
        password='9f81cc9ba8f6458d',  # 请修改为实际密码
        database='production_data'
    )
    
    # 连接数据库
    if not api.connect():
        print("数据库连接失败")
        return
    
    try:
        # 测试插入生产消息
        print("=== 测试 Line2 生产消息 ===")
        result = api.insert_line2_production_message('factory/line2/counter', 1)
        print(f"结果: {result}")
        
        # 测试插入故障数据
        print("\n=== 测试 Line2 故障数据 ===")
        result = api.insert_line2_malfunction_raw(2, 'A', 1)
        print(f"结果: {result}")
        
        # 获取生产计数
        print("\n=== 获取 Line2 生产计数 ===")
        result = api.get_line2_production_count()
        print(f"结果: {result}")
        
        # 获取故障统计
        print("\n=== 获取 Line2 故障统计 ===")
        result = api.get_line2_today_malfunction_stats()
        print(f"结果: {result}")
        
    finally:
        # 关闭连接
        api.disconnect()


if __name__ == "__main__":
    test_line2_api()
