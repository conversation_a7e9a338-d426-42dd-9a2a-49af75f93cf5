-- ===================================================================
-- 更新存储过程以支持 cmd 参数（增加/减少）
-- ===================================================================

USE production_data;

-- 删除现有的存储过程
DROP PROCEDURE IF EXISTS `increment_malfunction_count`;
DROP PROCEDURE IF EXISTS `process_malfunction_from_raw`;

-- 重新创建支持 cmd 参数的 increment_malfunction_count
delimiter ;;
CREATE PROCEDURE `increment_malfunction_count`(IN mal_type CHAR(1), IN mal_timestamp TIMESTAMP, IN mal_cmd TINYINT)
BEGIN
    DECLARE target_date DATE;
    DECLARE target_hour DATETIME;
    DECLARE current_product_id VARCHAR(255);
    DECLARE count_change INT;
    
    -- 检查类型是否有效（A-J）
    IF mal_type REGEXP '^[A-J]$' THEN
        -- 获取当前生产线的product_id
        SELECT product_id INTO current_product_id 
        FROM line1_production_lines 
        WHERE line_number = 1 
        LIMIT 1;
        
        SET target_date = DATE(mal_timestamp);
        SET target_hour = DATE_FORMAT(mal_timestamp, '%Y-%m-%d %H:00:00');
        
        -- 根据cmd确定计数变化：1=增加，0=减少
        SET count_change = IF(mal_cmd = 1, 1, -1);
        
        -- 更新每日计数，包含product_id
        INSERT INTO line1_malfunction_daily_count (product_id, malfunction_type, count, date_timestamp)
        VALUES (current_product_id, mal_type, count_change, target_date)
        ON DUPLICATE KEY UPDATE 
            count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
            updated_at = CURRENT_TIMESTAMP;
            
        -- 更新每小时计数，包含product_id
        INSERT INTO line1_malfunction_hourly_count (product_id, malfunction_type, count, hour_timestamp)
        VALUES (current_product_id, mal_type, count_change, target_hour)
        ON DUPLICATE KEY UPDATE 
            count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
            updated_at = CURRENT_TIMESTAMP;
    END IF;
END
;;
delimiter ;

-- 重新创建支持 cmd 参数的 process_malfunction_from_raw
delimiter ;;
CREATE PROCEDURE `process_malfunction_from_raw`(IN raw_id INT)
BEGIN
    DECLARE mal_type CHAR(1);
    DECLARE mal_timestamp TIMESTAMP;
    DECLARE target_date DATE;
    DECLARE target_hour DATETIME;
    DECLARE is_processed BOOLEAN DEFAULT FALSE;
    DECLARE current_product_id VARCHAR(255);
    DECLARE line_num INT;
    DECLARE mal_cmd TINYINT;
    DECLARE count_change INT;
    
    -- 获取原始数据，包含cmd字段
    SELECT status, timestamp, processed, line, cmd
    INTO mal_type, mal_timestamp, is_processed, line_num, mal_cmd
    FROM line1_malfunction_raw_data 
    WHERE id = raw_id;
    
    -- 如果数据存在且未处理过
    IF mal_type IS NOT NULL AND NOT is_processed THEN
        -- 获取对应生产线的product_id
        SELECT product_id INTO current_product_id 
        FROM line1_production_lines 
        WHERE line_number = line_num 
        LIMIT 1;
        
        -- 检查类型是否有效（A-J）
        IF mal_type REGEXP '^[A-J]$' THEN
            SET target_date = DATE(mal_timestamp);
            SET target_hour = DATE_FORMAT(mal_timestamp, '%Y-%m-%d %H:00:00');
            
            -- 根据cmd确定计数变化：1=增加，0=减少
            SET count_change = IF(mal_cmd = 1, 1, -1);
            
            -- 更新每日计数，包含product_id
            INSERT INTO line1_malfunction_daily_count (product_id, malfunction_type, count, date_timestamp)
            VALUES (current_product_id, mal_type, count_change, target_date)
            ON DUPLICATE KEY UPDATE 
                count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
                updated_at = CURRENT_TIMESTAMP;
                
            -- 更新每小时计数，包含product_id
            INSERT INTO line1_malfunction_hourly_count (product_id, malfunction_type, count, hour_timestamp)
            VALUES (current_product_id, mal_type, count_change, target_hour)
            ON DUPLICATE KEY UPDATE 
                count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
                updated_at = CURRENT_TIMESTAMP;
                
            -- 标记为已处理
            UPDATE line1_malfunction_raw_data 
            SET processed = TRUE 
            WHERE id = raw_id;
        END IF;
    END IF;
END
;;
delimiter ;

-- 验证存储过程创建成功
SHOW PROCEDURE STATUS WHERE Name IN ('increment_malfunction_count', 'process_malfunction_from_raw');

SELECT 'CMD parameter support updated successfully!' as status;
