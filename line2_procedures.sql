-- ===================================================================
-- Line2 Stored Procedures
-- 为 line2 创建相关存储过程
-- ===================================================================

USE production_data;

-- ----------------------------
-- Procedure structure for get_line2_hourly_malfunction_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_line2_hourly_malfunction_stats`;
delimiter ;;
CREATE PROCEDURE `get_line2_hourly_malfunction_stats`(IN target_hour DATETIME)
BEGIN
    SELECT 
        mti.type_code as malfunction_type,
        mti.type_name,
        COALESCE(mhc.count, 0) as count,
        mhc.product_id
    FROM line2_malfunction_type_info mti
    LEFT JOIN line2_malfunction_hourly_count mhc ON mti.type_code = mhc.malfunction_type 
        AND mhc.hour_timestamp = target_hour
    ORDER BY mti.type_code;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_line2_today_hourly_malfunction_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_line2_today_hourly_malfunction_stats`;
delimiter ;;
CREATE PROCEDURE `get_line2_today_hourly_malfunction_stats`()
BEGIN
    SELECT 
        mhc.malfunction_type,
        mti.type_name,
        mhc.hour_timestamp,
        HOUR(mhc.hour_timestamp) as hour,
        mhc.count,
        mhc.product_id
    FROM line2_malfunction_hourly_count mhc
    LEFT JOIN line2_malfunction_type_info mti ON mhc.malfunction_type = mti.type_code
    WHERE DATE(mhc.hour_timestamp) = CURDATE()
    ORDER BY mhc.hour_timestamp, mhc.malfunction_type;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_line2_today_malfunction_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_line2_today_malfunction_stats`;
delimiter ;;
CREATE PROCEDURE `get_line2_today_malfunction_stats`()
BEGIN
    SELECT 
        mdc.malfunction_type,
        mti.type_name,
        mdc.count,
        mdc.product_id
    FROM line2_malfunction_daily_count mdc
    LEFT JOIN line2_malfunction_type_info mti ON mdc.malfunction_type = mti.type_code
    WHERE mdc.date_timestamp = CURDATE()
    ORDER BY mdc.malfunction_type;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for increment_line2_malfunction_count
-- ----------------------------
DROP PROCEDURE IF EXISTS `increment_line2_malfunction_count`;
delimiter ;;
CREATE PROCEDURE `increment_line2_malfunction_count`(IN mal_type CHAR(1), IN mal_timestamp TIMESTAMP, IN mal_cmd TINYINT)
BEGIN
    DECLARE target_date DATE;
    DECLARE target_hour DATETIME;
    DECLARE current_product_id VARCHAR(255);
    DECLARE count_change INT;
    
    -- 检查类型是否有效（A-J）
    IF mal_type REGEXP '^[A-J]$' THEN
        -- 获取当前生产线的product_id
        SELECT product_id INTO current_product_id 
        FROM line1_production_lines 
        WHERE line_number = 2 
        LIMIT 1;
        
        SET target_date = DATE(mal_timestamp);
        SET target_hour = DATE_FORMAT(mal_timestamp, '%Y-%m-%d %H:00:00');
        
        -- 根据cmd确定计数变化：1=增加，0=减少
        SET count_change = IF(mal_cmd = 1, 1, -1);
        
        -- 更新每日计数，包含product_id
        INSERT INTO line2_malfunction_daily_count (product_id, malfunction_type, count, date_timestamp)
        VALUES (current_product_id, mal_type, count_change, target_date)
        ON DUPLICATE KEY UPDATE 
            count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
            updated_at = CURRENT_TIMESTAMP;
            
        -- 更新每小时计数，包含product_id
        INSERT INTO line2_malfunction_hourly_count (product_id, malfunction_type, count, hour_timestamp)
        VALUES (current_product_id, mal_type, count_change, target_hour)
        ON DUPLICATE KEY UPDATE 
            count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
            updated_at = CURRENT_TIMESTAMP;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for process_unprocessed_line2_malfunctions
-- ----------------------------
DROP PROCEDURE IF EXISTS `process_unprocessed_line2_malfunctions`;
delimiter ;;
CREATE PROCEDURE `process_unprocessed_line2_malfunctions`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE raw_id INT;
    DECLARE cur CURSOR FOR 
        SELECT id FROM line2_malfunction_raw_data WHERE processed = FALSE;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO raw_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        CALL process_line2_malfunction_from_raw(raw_id);
    END LOOP;
    CLOSE cur;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_line2_production_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_line2_production_stats`;
delimiter ;;
CREATE PROCEDURE `get_line2_production_stats`()
BEGIN
    -- 获取生产统计信息
    SELECT 
        'total_count' as stat_type,
        total_count as value,
        product_id,
        last_updated
    FROM line2_production_count
    
    UNION ALL
    
    SELECT 
        'today_count' as stat_type,
        COALESCE(count, 0) as value,
        product_id,
        CURDATE() as last_updated
    FROM line2_production_daily_count
    WHERE date_timestamp = CURDATE()
    
    UNION ALL
    
    SELECT 
        'current_hour_count' as stat_type,
        COALESCE(count, 0) as value,
        product_id,
        hour_timestamp as last_updated
    FROM line2_production_hourly_count
    WHERE hour_timestamp = DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00');
END
;;
delimiter ;

SELECT 'Line2 procedures created successfully!' as status;
