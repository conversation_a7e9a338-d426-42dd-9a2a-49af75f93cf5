-- ===================================================================
-- 更新现有数据以关联 line1_production_lines 的 product_id
-- ===================================================================

-- 获取当前生产线1的product_id
SET @current_product_id = (SELECT product_id FROM line1_production_lines WHERE line_number = 1 LIMIT 1);

-- 更新 line1_malfunction_daily_count 表
UPDATE line1_malfunction_daily_count 
SET product_id = @current_product_id 
WHERE product_id IS NULL OR product_id = '';

-- 更新 line1_malfunction_hourly_count 表
UPDATE line1_malfunction_hourly_count 
SET product_id = @current_product_id 
WHERE product_id IS NULL OR product_id = '';

-- 更新 line1_malfunction_raw_data 表
UPDATE line1_malfunction_raw_data 
SET product_id = @current_product_id 
WHERE product_id IS NULL OR product_id = '';

-- 更新 line1_malfunction_type_info 表
UPDATE line1_malfunction_type_info 
SET product_id = @current_product_id 
WHERE product_id IS NULL OR product_id = '';

-- 更新 line1_production_count 表
UPDATE line1_production_count 
SET product_id = @current_product_id 
WHERE product_id IS NULL OR product_id = '';

-- 更新 line1_production_daily_count 表
UPDATE line1_production_daily_count 
SET product_id = @current_product_id 
WHERE product_id IS NULL OR product_id = '';

-- 更新 line1_production_hourly_count 表
UPDATE line1_production_hourly_count 
SET product_id = @current_product_id 
WHERE product_id IS NULL OR product_id = '';

-- 更新 line1_production_message_log 表
UPDATE line1_production_message_log 
SET product_id = @current_product_id 
WHERE product_id IS NULL OR product_id = '';

-- 验证更新结果
SELECT 'line1_malfunction_daily_count' as table_name, COUNT(*) as total_records, 
       SUM(CASE WHEN product_id = @current_product_id THEN 1 ELSE 0 END) as updated_records
FROM line1_malfunction_daily_count
UNION ALL
SELECT 'line1_malfunction_hourly_count', COUNT(*), 
       SUM(CASE WHEN product_id = @current_product_id THEN 1 ELSE 0 END)
FROM line1_malfunction_hourly_count
UNION ALL
SELECT 'line1_malfunction_raw_data', COUNT(*), 
       SUM(CASE WHEN product_id = @current_product_id THEN 1 ELSE 0 END)
FROM line1_malfunction_raw_data
UNION ALL
SELECT 'line1_production_count', COUNT(*), 
       SUM(CASE WHEN product_id = @current_product_id THEN 1 ELSE 0 END)
FROM line1_production_count
UNION ALL
SELECT 'line1_production_daily_count', COUNT(*), 
       SUM(CASE WHEN product_id = @current_product_id THEN 1 ELSE 0 END)
FROM line1_production_daily_count
UNION ALL
SELECT 'line1_production_hourly_count', COUNT(*), 
       SUM(CASE WHEN product_id = @current_product_id THEN 1 ELSE 0 END)
FROM line1_production_hourly_count
UNION ALL
SELECT 'line1_production_message_log', COUNT(*), 
       SUM(CASE WHEN product_id = @current_product_id THEN 1 ELSE 0 END)
FROM line1_production_message_log;

-- 显示当前使用的product_id
SELECT CONCAT('当前生产线1使用的产品ID: ', @current_product_id) as info;
