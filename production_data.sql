/*
 Navicat Premium Dump SQL

 Source Server         : 本地数据库
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : localhost:3306
 Source Schema         : production_data

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 23/08/2025 13:58:51
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for line1_malfunction_daily_count
-- ----------------------------
DROP TABLE IF EXISTS `line1_malfunction_daily_count`;
CREATE TABLE `line1_malfunction_daily_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `malfunction_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '坏机类型 A-J',
  `count` int(11) NOT NULL DEFAULT 0 COMMENT '坏机次数',
  `date_timestamp` date NOT NULL COMMENT '统计日期',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_product_type_date`(`product_id`, `malfunction_type`, `date_timestamp`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 550 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of line1_malfunction_daily_count
-- ----------------------------

-- ----------------------------
-- Table structure for line1_malfunction_hourly_count
-- ----------------------------
DROP TABLE IF EXISTS `line1_malfunction_hourly_count`;
CREATE TABLE `line1_malfunction_hourly_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `malfunction_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '坏机类型 A-J',
  `count` int(11) NOT NULL DEFAULT 0 COMMENT '坏机次数',
  `hour_timestamp` datetime NOT NULL COMMENT '小时时间戳 (YYYY-MM-DD HH:00:00)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_product_type_hour`(`product_id`, `malfunction_type`, `hour_timestamp`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 617 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of line1_malfunction_hourly_count
-- ----------------------------

-- ----------------------------
-- Table structure for line1_malfunction_raw_data
-- ----------------------------
DROP TABLE IF EXISTS `line1_malfunction_raw_data`;
CREATE TABLE `line1_malfunction_raw_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `line` int(11) NOT NULL COMMENT '生产线编号',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '故障状态类型 A-J',
  `cmd` tinyint(4) NOT NULL COMMENT '操作命令：1=增加，0=减少',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
  `processed` tinyint(1) NULL DEFAULT 0 COMMENT '是否已处理到统计表',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_line`(`line`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_timestamp`(`timestamp`) USING BTREE,
  INDEX `idx_processed`(`processed`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 618 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of line1_malfunction_raw_data
-- ----------------------------

-- ----------------------------
-- Table structure for line1_malfunction_type_info
-- ----------------------------
DROP TABLE IF EXISTS `line1_malfunction_type_info`;
CREATE TABLE `line1_malfunction_type_info`  (
  `type_code` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '坏机类型代码 A-J',
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '坏机类型名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '类型描述',
  PRIMARY KEY (`type_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of line1_malfunction_type_info
-- ----------------------------
INSERT INTO `line1_malfunction_type_info` VALUES ('A', '', '假焊', NULL);
INSERT INTO `line1_malfunction_type_info` VALUES ('B', '', '连锡', NULL);
INSERT INTO `line1_malfunction_type_info` VALUES ('C', '', '错料', NULL);
INSERT INTO `line1_malfunction_type_info` VALUES ('D', NULL, '漏料', NULL);
INSERT INTO `line1_malfunction_type_info` VALUES ('E', NULL, '反装', NULL);
INSERT INTO `line1_malfunction_type_info` VALUES ('F', NULL, '坏料', NULL);
INSERT INTO `line1_malfunction_type_info` VALUES ('G', NULL, '功能坏', NULL);
INSERT INTO `line1_malfunction_type_info` VALUES ('H', NULL, '开路', NULL);
INSERT INTO `line1_malfunction_type_info` VALUES ('I', NULL, '短路', NULL);
INSERT INTO `line1_malfunction_type_info` VALUES ('J', NULL, '其他', NULL);

-- ----------------------------
-- Table structure for line1_production_count
-- ----------------------------
DROP TABLE IF EXISTS `line1_production_count`;
CREATE TABLE `line1_production_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `total_count` int(11) NOT NULL DEFAULT 0,
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of line1_production_count
-- ----------------------------

-- ----------------------------
-- Table structure for line1_production_daily_count
-- ----------------------------
DROP TABLE IF EXISTS `line1_production_daily_count`;
CREATE TABLE `line1_production_daily_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `date_timestamp` date NOT NULL,
  `count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_product_date`(`product_id`, `date_timestamp`) USING BTREE,
  INDEX `idx_line1_daily_timestamp`(`date_timestamp`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of line1_production_daily_count
-- ----------------------------

-- ----------------------------
-- Table structure for line1_production_hourly_count
-- ----------------------------
DROP TABLE IF EXISTS `line1_production_hourly_count`;
CREATE TABLE `line1_production_hourly_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `hour_timestamp` datetime NOT NULL,
  `count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_product_hour`(`product_id`, `hour_timestamp`) USING BTREE,
  INDEX `idx_line1_hourly_timestamp`(`hour_timestamp`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of line1_production_hourly_count
-- ----------------------------

-- ----------------------------
-- Table structure for line1_production_lines
-- ----------------------------
DROP TABLE IF EXISTS `line1_production_lines`;
CREATE TABLE `line1_production_lines`  (
  `line_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '生产线ID',
  `line_number` int(11) NOT NULL COMMENT '生产线编号',
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品号',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`line_id`) USING BTREE,
  UNIQUE INDEX `line_number`(`line_number`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '生产线基础信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of line1_production_lines
-- ----------------------------
INSERT INTO `line1_production_lines` VALUES (1, 1, '300-596-203', '2025-08-23 13:58:40', '2025-08-23 13:58:40');

-- ----------------------------
-- Table structure for line1_production_message_log
-- ----------------------------
DROP TABLE IF EXISTS `line1_production_message_log`;
CREATE TABLE `line1_production_message_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `topic` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `qos` int(11) NULL DEFAULT 0,
  `counter` int(11) NULL DEFAULT NULL,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `received_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_line1_message_topic`(`topic`) USING BTREE,
  INDEX `idx_line1_message_timestamp`(`received_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 802 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of line1_production_message_log
-- ----------------------------

-- ----------------------------
-- View structure for view_current_hour_malfunction
-- ----------------------------
DROP VIEW IF EXISTS `view_current_hour_malfunction`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_current_hour_malfunction` AS select `mti`.`type_code` AS `malfunction_type`,`mti`.`type_name` AS `type_name`,coalesce(`mhc`.`count`,0) AS `count` from (`line1_malfunction_type_info` `mti` left join `line1_malfunction_hourly_count` `mhc` on(((`mti`.`type_code` = `mhc`.`malfunction_type`) and (`mhc`.`hour_timestamp` = date_format(now(),'%Y-%m-%d %H:00:00'))))) order by coalesce(`mhc`.`count`,0) desc,`mti`.`type_code`;

-- ----------------------------
-- View structure for view_line1_current_count
-- ----------------------------
DROP VIEW IF EXISTS `view_line1_current_count`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_line1_current_count` AS select `line1_production_count`.`total_count` AS `total_count`,`line1_production_count`.`last_updated` AS `last_updated` from `line1_production_count`;

-- ----------------------------
-- View structure for view_line1_recent_daily
-- ----------------------------
DROP VIEW IF EXISTS `view_line1_recent_daily`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_line1_recent_daily` AS select `line1_production_daily_count`.`date_timestamp` AS `date_timestamp`,`line1_production_daily_count`.`count` AS `count` from `line1_production_daily_count` where (`line1_production_daily_count`.`date_timestamp` >= (curdate() - interval 6 day)) order by `line1_production_daily_count`.`date_timestamp`;

-- ----------------------------
-- View structure for view_line1_today_hourly
-- ----------------------------
DROP VIEW IF EXISTS `view_line1_today_hourly`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_line1_today_hourly` AS select `line1_production_hourly_count`.`hour_timestamp` AS `hour_timestamp`,`line1_production_hourly_count`.`count` AS `count`,hour(`line1_production_hourly_count`.`hour_timestamp`) AS `hour` from `line1_production_hourly_count` where (cast(`line1_production_hourly_count`.`hour_timestamp` as date) = curdate()) order by `line1_production_hourly_count`.`hour_timestamp`;

-- ----------------------------
-- View structure for view_malfunction_ranking
-- ----------------------------
DROP VIEW IF EXISTS `view_malfunction_ranking`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_malfunction_ranking` AS select `mti`.`type_code` AS `malfunction_type`,`mti`.`type_name` AS `type_name`,coalesce(`mdc`.`count`,0) AS `count` from (`line1_malfunction_type_info` `mti` left join `line1_malfunction_daily_count` `mdc` on(((`mti`.`type_code` = `mdc`.`malfunction_type`) and (`mdc`.`date_timestamp` = curdate())))) order by coalesce(`mdc`.`count`,0) desc,`mti`.`type_code`;

-- ----------------------------
-- View structure for view_today_hourly_malfunction
-- ----------------------------
DROP VIEW IF EXISTS `view_today_hourly_malfunction`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_today_hourly_malfunction` AS select `mhc`.`hour_timestamp` AS `hour_timestamp`,hour(`mhc`.`hour_timestamp`) AS `hour`,sum(`mhc`.`count`) AS `total_count`,group_concat(concat(`mhc`.`malfunction_type`,':',`mhc`.`count`) order by `mhc`.`malfunction_type` ASC separator ',') AS `type_breakdown` from `line1_malfunction_hourly_count` `mhc` where (cast(`mhc`.`hour_timestamp` as date) = curdate()) group by `mhc`.`hour_timestamp` order by `mhc`.`hour_timestamp`;

-- ----------------------------
-- View structure for view_today_malfunction
-- ----------------------------
DROP VIEW IF EXISTS `view_today_malfunction`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_today_malfunction` AS select `mti`.`type_code` AS `malfunction_type`,`mti`.`type_name` AS `type_name`,coalesce(`mdc`.`count`,0) AS `count` from (`line1_malfunction_type_info` `mti` left join `line1_malfunction_daily_count` `mdc` on(((`mti`.`type_code` = `mdc`.`malfunction_type`) and (`mdc`.`date_timestamp` = curdate())))) order by `mti`.`type_code`;

-- ----------------------------
-- Procedure structure for get_hourly_malfunction_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_hourly_malfunction_stats`;
delimiter ;;
CREATE PROCEDURE `get_hourly_malfunction_stats`(IN target_hour DATETIME)
BEGIN
    SELECT
        mti.type_code as malfunction_type,
        mti.type_name,
        COALESCE(mhc.count, 0) as count
    FROM line1_malfunction_type_info mti
    LEFT JOIN line1_malfunction_hourly_count mhc ON mti.type_code = mhc.malfunction_type
        AND mhc.hour_timestamp = target_hour
    ORDER BY mti.type_code;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_today_hourly_malfunction_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_today_hourly_malfunction_stats`;
delimiter ;;
CREATE PROCEDURE `get_today_hourly_malfunction_stats`()
BEGIN
    SELECT
        mhc.malfunction_type,
        mti.type_name,
        mhc.hour_timestamp,
        HOUR(mhc.hour_timestamp) as hour,
        mhc.count
    FROM line1_malfunction_hourly_count mhc
    LEFT JOIN line1_malfunction_type_info mti ON mhc.malfunction_type = mti.type_code
    WHERE DATE(mhc.hour_timestamp) = CURDATE()
    ORDER BY mhc.hour_timestamp, mhc.malfunction_type;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_today_malfunction_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_today_malfunction_stats`;
delimiter ;;
CREATE PROCEDURE `get_today_malfunction_stats`()
BEGIN
    SELECT
        mdc.malfunction_type,
        mti.type_name,
        mdc.count
    FROM line1_malfunction_daily_count mdc
    LEFT JOIN line1_malfunction_type_info mti ON mdc.malfunction_type = mti.type_code
    WHERE mdc.date_timestamp = CURDATE()
    ORDER BY mdc.malfunction_type;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for increment_malfunction_count
-- ----------------------------
DROP PROCEDURE IF EXISTS `increment_malfunction_count`;
delimiter ;;
CREATE PROCEDURE `increment_malfunction_count`(IN mal_type CHAR(1), IN mal_timestamp TIMESTAMP, IN mal_cmd TINYINT)
BEGIN
    DECLARE target_date DATE;
    DECLARE target_hour DATETIME;
    DECLARE current_product_id VARCHAR(255);
    DECLARE count_change INT;

    -- 检查类型是否有效（A-J）
    IF mal_type REGEXP '^[A-J]$' THEN
        -- 获取当前生产线的product_id
        SELECT product_id INTO current_product_id
        FROM line1_production_lines
        WHERE line_number = 1
        LIMIT 1;

        SET target_date = DATE(mal_timestamp);
        SET target_hour = DATE_FORMAT(mal_timestamp, '%Y-%m-%d %H:00:00');

        -- 根据cmd确定计数变化：1=增加，0=减少
        SET count_change = IF(mal_cmd = 1, 1, -1);

        -- 更新每日计数，包含product_id
        INSERT INTO line1_malfunction_daily_count (product_id, malfunction_type, count, date_timestamp)
        VALUES (current_product_id, mal_type, count_change, target_date)
        ON DUPLICATE KEY UPDATE
            count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
            updated_at = CURRENT_TIMESTAMP;

        -- 更新每小时计数，包含product_id
        INSERT INTO line1_malfunction_hourly_count (product_id, malfunction_type, count, hour_timestamp)
        VALUES (current_product_id, mal_type, count_change, target_hour)
        ON DUPLICATE KEY UPDATE
            count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
            updated_at = CURRENT_TIMESTAMP;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for insert_line1_malfunction_raw
-- ----------------------------
DROP PROCEDURE IF EXISTS `insert_line1_malfunction_raw`;
delimiter ;;
CREATE PROCEDURE `insert_line1_malfunction_raw`(
    IN line_num INT,
    IN mal_status CHAR(1),
    IN mal_cmd TINYINT,
    IN mal_timestamp TIMESTAMP)
BEGIN
    DECLARE new_id INT;
    DECLARE current_product_id VARCHAR(255);

    -- 获取对应生产线的product_id
    SET current_product_id = get_line_product_id(line_num);

    -- 插入原始数据，包含product_id
    INSERT INTO line1_malfunction_raw_data
    (product_id, line, status, cmd, timestamp)
    VALUES (current_product_id, line_num, mal_status, mal_cmd, mal_timestamp);

    -- 获取新插入的ID
    SET new_id = LAST_INSERT_ID();

    -- 自动处理统计
    CALL process_malfunction_from_raw(new_id);
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for process_line1_counter
-- ----------------------------
DROP PROCEDURE IF EXISTS `process_line1_counter`;
delimiter ;;
CREATE PROCEDURE `process_line1_counter`(IN msg_topic VARCHAR(100), IN msg_time TIMESTAMP)
BEGIN
    DECLARE current_hour DATETIME;
    DECLARE current_day DATE;
    DECLARE hour_count INT;
    DECLARE day_count INT;
    DECLARE current_product_id VARCHAR(255);

    -- 只处理line1/counter消息
    IF msg_topic = 'factory/line1/counter' THEN
        -- 获取当前生产线的product_id
        SELECT product_id INTO current_product_id
        FROM line1_production_lines
        WHERE line_number = 1
        LIMIT 1;

        -- 更新总计数，包含product_id
        INSERT INTO line1_production_count (product_id, total_count, last_updated)
        VALUES (current_product_id, 1, msg_time)
        ON DUPLICATE KEY UPDATE
            total_count = total_count + 1,
            last_updated = msg_time;

        -- 获取当前小时和日期
        SET current_hour = DATE_FORMAT(msg_time, '%Y-%m-%d %H:00:00');
        SET current_day = DATE(msg_time);

        -- 检查小时记录是否存在
        SELECT COUNT(*) INTO hour_count FROM line1_production_hourly_count
        WHERE hour_timestamp = current_hour AND product_id = current_product_id;

        -- 更新或插入小时计数，包含product_id
        IF hour_count > 0 THEN
            UPDATE line1_production_hourly_count
            SET count = count + 1
            WHERE hour_timestamp = current_hour AND product_id = current_product_id;
        ELSE
            INSERT INTO line1_production_hourly_count (product_id, hour_timestamp, count)
            VALUES (current_product_id, current_hour, 1);
        END IF;

        -- 检查日记录是否存在
        SELECT COUNT(*) INTO day_count FROM line1_production_daily_count
        WHERE date_timestamp = current_day AND product_id = current_product_id;

        -- 更新或插入日计数，包含product_id
        IF day_count > 0 THEN
            UPDATE line1_production_daily_count
            SET count = count + 1
            WHERE date_timestamp = current_day AND product_id = current_product_id;
        ELSE
            INSERT INTO line1_production_daily_count (product_id, date_timestamp, count)
            VALUES (current_product_id, current_day, 1);
        END IF;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for process_malfunction_from_raw
-- ----------------------------
DROP PROCEDURE IF EXISTS `process_malfunction_from_raw`;
delimiter ;;
CREATE PROCEDURE `process_malfunction_from_raw`(IN raw_id INT)
BEGIN
    DECLARE mal_type CHAR(1);
    DECLARE mal_timestamp TIMESTAMP;
    DECLARE target_date DATE;
    DECLARE target_hour DATETIME;
    DECLARE is_processed BOOLEAN DEFAULT FALSE;
    DECLARE current_product_id VARCHAR(255);
    DECLARE line_num INT;
    DECLARE mal_cmd TINYINT;
    DECLARE count_change INT;

    -- 获取原始数据，包含cmd字段
    SELECT status, timestamp, processed, line, cmd
    INTO mal_type, mal_timestamp, is_processed, line_num, mal_cmd
    FROM line1_malfunction_raw_data
    WHERE id = raw_id;

    -- 如果数据存在且未处理过
    IF mal_type IS NOT NULL AND NOT is_processed THEN
        -- 获取对应生产线的product_id
        SELECT product_id INTO current_product_id
        FROM line1_production_lines
        WHERE line_number = line_num
        LIMIT 1;

        -- 检查类型是否有效（A-J）
        IF mal_type REGEXP '^[A-J]$' THEN
            SET target_date = DATE(mal_timestamp);
            SET target_hour = DATE_FORMAT(mal_timestamp, '%Y-%m-%d %H:00:00');

            -- 根据cmd确定计数变化：1=增加，0=减少
            SET count_change = IF(mal_cmd = 1, 1, -1);

            -- 更新每日计数，包含product_id
            INSERT INTO line1_malfunction_daily_count (product_id, malfunction_type, count, date_timestamp)
            VALUES (current_product_id, mal_type, count_change, target_date)
            ON DUPLICATE KEY UPDATE
                count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
                updated_at = CURRENT_TIMESTAMP;

            -- 更新每小时计数，包含product_id
            INSERT INTO line1_malfunction_hourly_count (product_id, malfunction_type, count, hour_timestamp)
            VALUES (current_product_id, mal_type, count_change, target_hour)
            ON DUPLICATE KEY UPDATE
                count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
                updated_at = CURRENT_TIMESTAMP;

            -- 标记为已处理
            UPDATE line1_malfunction_raw_data
            SET processed = TRUE
            WHERE id = raw_id;
        END IF;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for process_unprocessed_malfunctions
-- ----------------------------
DROP PROCEDURE IF EXISTS `process_unprocessed_malfunctions`;
delimiter ;;
CREATE PROCEDURE `process_unprocessed_malfunctions`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE raw_id INT;
    DECLARE cur CURSOR FOR 
        SELECT id FROM malfunction_raw_data WHERE processed = FALSE;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO raw_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        CALL process_malfunction_from_raw(raw_id);
    END LOOP;
    CLOSE cur;
END
;;
delimiter ;

-- ----------------------------
-- Function to get current product_id for line
-- ----------------------------
DROP FUNCTION IF EXISTS `get_line_product_id`;
delimiter ;;
CREATE FUNCTION `get_line_product_id`(line_num INT) RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result_product_id VARCHAR(255);

    SELECT product_id INTO result_product_id
    FROM line1_production_lines
    WHERE line_number = line_num
    LIMIT 1;

    RETURN result_product_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table line1_production_message_log
-- ----------------------------
DROP TRIGGER IF EXISTS `trigger_line1_counter`;
delimiter ;;
CREATE TRIGGER `trigger_line1_counter` BEFORE INSERT ON `line1_production_message_log` FOR EACH ROW BEGIN
    DECLARE current_product_id VARCHAR(255);

    IF NEW.topic = 'factory/line1/counter' THEN
        -- 获取当前生产线的product_id并设置到NEW记录中
        SET current_product_id = get_line_product_id(1);
        SET NEW.product_id = current_product_id;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table line1_production_message_log (AFTER INSERT)
-- ----------------------------
DROP TRIGGER IF EXISTS `trigger_line1_counter_after`;
delimiter ;;
CREATE TRIGGER `trigger_line1_counter_after` AFTER INSERT ON `line1_production_message_log` FOR EACH ROW BEGIN
    IF NEW.topic = 'factory/line1/counter' THEN
        -- 处理计数
        CALL process_line1_counter(NEW.topic, NEW.received_at);
    END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
