# CMD 参数功能说明

## 概述
`cmd` 参数用于控制故障计数的增加或减少：
- **cmd = 1**: 增加故障计数
- **cmd = 0**: 减少故障计数

## 应用更新

### 1. 更新数据库存储过程
```sql
-- 应用 cmd 参数支持
SOURCE update_cmd_support.sql;
```

### 2. 移除触发器（如果还没有执行）
```sql
-- 移除触发器避免冲突
SOURCE remove_triggers.sql;
```

## API 使用示例

### 增加故障计数
```python
import requests

# 增加假焊故障计数
data = {
    "line_number": 1,
    "status": "A",
    "cmd": 1  # 增加
}

response = requests.post("http://localhost:5000/api/malfunction/raw", json=data)
print(response.json())
```

### 减少故障计数
```python
import requests

# 减少假焊故障计数
data = {
    "line_number": 1,
    "status": "A", 
    "cmd": 0  # 减少
}

response = requests.post("http://localhost:5000/api/malfunction/raw", json=data)
print(response.json())
```

### curl 示例
```bash
# 增加故障计数
curl -X POST http://localhost:5000/api/malfunction/raw \
  -H "Content-Type: application/json" \
  -d '{"line_number": 1, "status": "A", "cmd": 1}'

# 减少故障计数
curl -X POST http://localhost:5000/api/malfunction/raw \
  -H "Content-Type: application/json" \
  -d '{"line_number": 1, "status": "A", "cmd": 0}'
```

## 数据库层面的处理

### 存储过程修改
1. **`process_malfunction_from_raw`**: 
   - 现在读取 `cmd` 字段
   - 根据 cmd 值计算计数变化（+1 或 -1）
   - 使用 `GREATEST(0, count + count_change)` 确保计数不会小于0

2. **`increment_malfunction_count`**:
   - 添加了 `mal_cmd` 参数
   - 支持增加和减少操作

### 计数保护
- 使用 `GREATEST(0, count + count_change)` 确保计数永远不会小于0
- 即使减少操作超过当前计数，结果也会是0而不是负数

## 业务场景

### 典型使用场景
1. **故障发生**: `cmd=1` 增加故障计数
2. **故障修复**: `cmd=0` 减少故障计数
3. **误报纠正**: `cmd=0` 减少错误记录的故障

### 数据流程
```
上报数据 → API接收 → 存储原始数据 → 调用存储过程 → 更新统计表
```

## 测试验证

### 运行测试脚本
```bash
python test_cmd_functionality.py
```

### 手动验证
```python
import requests

BASE_URL = "http://localhost:5000"

# 1. 增加3次A类故障
for i in range(3):
    requests.post(f"{BASE_URL}/api/malfunction/raw", 
                  json={"line_number": 1, "status": "A", "cmd": 1})

# 2. 减少1次A类故障  
requests.post(f"{BASE_URL}/api/malfunction/raw",
              json={"line_number": 1, "status": "A", "cmd": 0})

# 结果：A类故障计数应该是2
```

### 数据库验证
```sql
-- 查看今日故障统计
SELECT 
    malfunction_type,
    count,
    date_timestamp
FROM line1_malfunction_daily_count 
WHERE date_timestamp = CURDATE()
ORDER BY malfunction_type;

-- 查看原始数据记录
SELECT 
    status,
    cmd,
    timestamp,
    processed
FROM line1_malfunction_raw_data 
ORDER BY id DESC 
LIMIT 10;
```

## 注意事项

1. **参数验证**: API会验证 cmd 只能是 0 或 1
2. **计数保护**: 数据库层面确保计数不会小于0
3. **原始数据**: 所有操作都会记录在原始数据表中
4. **product_id**: 自动从生产线配置中获取并关联

## 错误处理

### 无效的 cmd 值
```json
{
  "success": false,
  "error": "操作命令必须是0或1"
}
```

### 数据库错误
```json
{
  "success": false, 
  "error": "数据库连接失败"
}
```

## 完整示例

```python
import requests
import time

BASE_URL = "http://localhost:5000"

def simulate_malfunction_lifecycle():
    """模拟故障的完整生命周期"""
    
    # 1. 故障发生 - 增加计数
    print("1. 故障发生...")
    response = requests.post(f"{BASE_URL}/api/malfunction/raw", 
                           json={"line_number": 1, "status": "A", "cmd": 1})
    print(f"增加结果: {response.json()}")
    
    time.sleep(1)
    
    # 2. 故障修复 - 减少计数
    print("2. 故障修复...")
    response = requests.post(f"{BASE_URL}/api/malfunction/raw",
                           json={"line_number": 1, "status": "A", "cmd": 0})
    print(f"减少结果: {response.json()}")

if __name__ == "__main__":
    simulate_malfunction_lifecycle()
```
