-- ===================================================================
-- Line2 Views
-- 为 line2 创建相关视图
-- ===================================================================

USE production_data;

-- ----------------------------
-- View structure for view_line2_current_count
-- ----------------------------
DROP VIEW IF EXISTS `view_line2_current_count`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_line2_current_count` AS 
SELECT 
    `line2_production_count`.`total_count` AS `total_count`,
    `line2_production_count`.`last_updated` AS `last_updated`,
    `line2_production_count`.`product_id` AS `product_id`
FROM `line2_production_count`;

-- ----------------------------
-- View structure for view_line2_recent_daily
-- ----------------------------
DROP VIEW IF EXISTS `view_line2_recent_daily`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_line2_recent_daily` AS 
SELECT 
    `line2_production_daily_count`.`date_timestamp` AS `date_timestamp`,
    `line2_production_daily_count`.`count` AS `count`,
    `line2_production_daily_count`.`product_id` AS `product_id`
FROM `line2_production_daily_count` 
WHERE (`line2_production_daily_count`.`date_timestamp` >= (curdate() - interval 6 day)) 
ORDER BY `line2_production_daily_count`.`date_timestamp`;

-- ----------------------------
-- View structure for view_line2_today_hourly
-- ----------------------------
DROP VIEW IF EXISTS `view_line2_today_hourly`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_line2_today_hourly` AS 
SELECT 
    `line2_production_hourly_count`.`hour_timestamp` AS `hour_timestamp`,
    `line2_production_hourly_count`.`count` AS `count`,
    hour(`line2_production_hourly_count`.`hour_timestamp`) AS `hour`,
    `line2_production_hourly_count`.`product_id` AS `product_id`
FROM `line2_production_hourly_count` 
WHERE (cast(`line2_production_hourly_count`.`hour_timestamp` as date) = curdate()) 
ORDER BY `line2_production_hourly_count`.`hour_timestamp`;

-- ----------------------------
-- View structure for view_line2_malfunction_ranking
-- ----------------------------
DROP VIEW IF EXISTS `view_line2_malfunction_ranking`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_line2_malfunction_ranking` AS 
SELECT 
    `mti`.`type_code` AS `malfunction_type`,
    `mti`.`type_name` AS `type_name`,
    coalesce(`mdc`.`count`,0) AS `count`,
    `mdc`.`product_id` AS `product_id`
FROM (`line2_malfunction_type_info` `mti` 
LEFT JOIN `line2_malfunction_daily_count` `mdc` ON(((`mti`.`type_code` = `mdc`.`malfunction_type`) 
AND (`mdc`.`date_timestamp` = curdate())))) 
ORDER BY coalesce(`mdc`.`count`,0) desc,`mti`.`type_code`;

-- ----------------------------
-- View structure for view_line2_today_malfunction
-- ----------------------------
DROP VIEW IF EXISTS `view_line2_today_malfunction`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_line2_today_malfunction` AS 
SELECT 
    `mti`.`type_code` AS `malfunction_type`,
    `mti`.`type_name` AS `type_name`,
    coalesce(`mdc`.`count`,0) AS `count`,
    `mdc`.`product_id` AS `product_id`
FROM (`line2_malfunction_type_info` `mti` 
LEFT JOIN `line2_malfunction_daily_count` `mdc` ON(((`mti`.`type_code` = `mdc`.`malfunction_type`) 
AND (`mdc`.`date_timestamp` = curdate())))) 
ORDER BY `mti`.`type_code`;

-- ----------------------------
-- View structure for view_line2_today_hourly_malfunction
-- ----------------------------
DROP VIEW IF EXISTS `view_line2_today_hourly_malfunction`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_line2_today_hourly_malfunction` AS 
SELECT 
    `mhc`.`hour_timestamp` AS `hour_timestamp`,
    hour(`mhc`.`hour_timestamp`) AS `hour`,
    sum(`mhc`.`count`) AS `total_count`,
    group_concat(concat(`mhc`.`malfunction_type`,':',`mhc`.`count`) order by `mhc`.`malfunction_type` ASC separator ',') AS `type_breakdown`,
    `mhc`.`product_id` AS `product_id`
FROM `line2_malfunction_hourly_count` `mhc` 
WHERE (cast(`mhc`.`hour_timestamp` as date) = curdate()) 
GROUP BY `mhc`.`hour_timestamp`, `mhc`.`product_id`
ORDER BY `mhc`.`hour_timestamp`;

-- ----------------------------
-- View structure for view_line2_current_hour_malfunction
-- ----------------------------
DROP VIEW IF EXISTS `view_line2_current_hour_malfunction`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `view_line2_current_hour_malfunction` AS 
SELECT 
    `mti`.`type_code` AS `malfunction_type`,
    `mti`.`type_name` AS `type_name`,
    coalesce(`mhc`.`count`,0) AS `count`,
    `mhc`.`product_id` AS `product_id`
FROM (`line2_malfunction_type_info` `mti` 
LEFT JOIN `line2_malfunction_hourly_count` `mhc` ON(((`mti`.`type_code` = `mhc`.`malfunction_type`) 
AND (`mhc`.`hour_timestamp` = date_format(now(),'%Y-%m-%d %H:00:00'))))) 
ORDER BY coalesce(`mhc`.`count`,0) desc,`mti`.`type_code`;

SELECT 'Line2 views created successfully!' as status;
