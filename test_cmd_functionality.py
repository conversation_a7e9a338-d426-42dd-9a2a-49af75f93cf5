#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 cmd 参数功能
验证故障计数的增加和减少功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_malfunction_increase_decrease():
    """测试故障计数的增加和减少"""
    print("=== 测试故障计数增加和减少功能 ===")
    
    # 测试增加故障计数 (cmd=1)
    print("\n1. 增加故障计数 (cmd=1):")
    for i in range(3):
        data = {
            "line_number": 1,
            "status": "A",
            "cmd": 1  # 增加
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/malfunction/raw", json=data)
            if response.status_code == 200:
                result = response.json()
                print(f"   ✓ 第{i+1}次增加成功，ID: {result.get('malfunction_id')}")
            else:
                print(f"   ✗ 第{i+1}次增加失败: {response.json()}")
        except Exception as e:
            print(f"   ✗ 第{i+1}次增加错误: {e}")
        
        time.sleep(0.5)
    
    # 测试减少故障计数 (cmd=0)
    print("\n2. 减少故障计数 (cmd=0):")
    for i in range(2):
        data = {
            "line_number": 1,
            "status": "A", 
            "cmd": 0  # 减少
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/malfunction/raw", json=data)
            if response.status_code == 200:
                result = response.json()
                print(f"   ✓ 第{i+1}次减少成功，ID: {result.get('malfunction_id')}")
            else:
                print(f"   ✗ 第{i+1}次减少失败: {response.json()}")
        except Exception as e:
            print(f"   ✗ 第{i+1}次减少错误: {e}")
        
        time.sleep(0.5)

def test_different_malfunction_types():
    """测试不同故障类型的增减"""
    print("\n=== 测试不同故障类型 ===")
    
    malfunction_types = [
        {"status": "A", "name": "假焊"},
        {"status": "B", "name": "连锡"},
        {"status": "C", "name": "错料"}
    ]
    
    for mal in malfunction_types:
        print(f"\n测试 {mal['name']}({mal['status']}):")
        
        # 增加2次
        for i in range(2):
            data = {
                "line_number": 1,
                "status": mal["status"],
                "cmd": 1
            }
            
            try:
                response = requests.post(f"{BASE_URL}/api/malfunction/raw", json=data)
                if response.status_code == 200:
                    print(f"   ✓ 增加第{i+1}次成功")
                else:
                    print(f"   ✗ 增加第{i+1}次失败")
            except Exception as e:
                print(f"   ✗ 增加第{i+1}次错误: {e}")
        
        # 减少1次
        data = {
            "line_number": 1,
            "status": mal["status"],
            "cmd": 0
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/malfunction/raw", json=data)
            if response.status_code == 200:
                print(f"   ✓ 减少1次成功")
            else:
                print(f"   ✗ 减少1次失败")
        except Exception as e:
            print(f"   ✗ 减少1次错误: {e}")

def test_zero_boundary():
    """测试计数不会小于0的边界情况"""
    print("\n=== 测试零边界情况 ===")
    
    # 尝试减少一个可能为0的计数
    data = {
        "line_number": 1,
        "status": "J",  # 使用J类型，可能之前没有记录
        "cmd": 0  # 减少
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/malfunction/raw", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✓ 零边界测试成功，ID: {result.get('malfunction_id')}")
            print("   注意: 数据库应该确保计数不会小于0")
        else:
            print(f"   ✗ 零边界测试失败: {response.json()}")
    except Exception as e:
        print(f"   ✗ 零边界测试错误: {e}")

def test_cmd_parameter_validation():
    """测试cmd参数验证"""
    print("\n=== 测试cmd参数验证 ===")
    
    # 测试无效的cmd值
    invalid_cmds = [2, -1, "invalid"]
    
    for cmd in invalid_cmds:
        data = {
            "line_number": 1,
            "status": "A",
            "cmd": cmd
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/malfunction/raw", json=data)
            print(f"   cmd={cmd}: 状态码={response.status_code}")
            if response.status_code != 200:
                print(f"   ✓ 正确拒绝了无效的cmd值: {cmd}")
            else:
                print(f"   ⚠ 意外接受了无效的cmd值: {cmd}")
        except Exception as e:
            print(f"   cmd={cmd} 请求错误: {e}")

def main():
    """主测试函数"""
    print("开始测试 cmd 参数功能...")
    print("cmd=1: 增加故障计数")
    print("cmd=0: 减少故障计数")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未正常运行")
            return
    except:
        print("❌ 无法连接到服务器，请确保 minimal_api.py 正在运行")
        return
    
    print("✅ 服务器运行正常")
    
    # 运行测试
    test_malfunction_increase_decrease()
    test_different_malfunction_types()
    test_zero_boundary()
    test_cmd_parameter_validation()
    
    print("\n🎉 cmd 参数功能测试完成！")
    print("\n💡 提示:")
    print("- cmd=1 表示增加故障计数")
    print("- cmd=0 表示减少故障计数")
    print("- 计数不会小于0（使用GREATEST函数保护）")

if __name__ == "__main__":
    main()
