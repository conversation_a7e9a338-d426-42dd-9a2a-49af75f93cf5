#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
测试生产数据插入接口的各种功能
"""

import requests
import json
from datetime import datetime
import time

# API服务器地址
BASE_URL = "http://localhost:5000"

def test_health_check():
    """测试健康检查"""
    print("=== 测试健康检查 ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_insert_production_message():
    """测试插入生产消息"""
    print("\n=== 测试插入生产消息 ===")
    
    # 测试数据
    test_data = {
        "topic": "factory/line1/counter",
        "counter": 1
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/production/message",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"插入生产消息失败: {e}")
        return False

def test_insert_malfunction():
    """测试插入故障数据"""
    print("\n=== 测试插入故障数据 ===")
    
    # 测试数据
    test_data = {
        "line_number": 1,
        "status": "A",
        "cmd": 1
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/malfunction/raw",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"插入故障数据失败: {e}")
        return False

def test_get_production_count():
    """测试获取生产计数"""
    print("\n=== 测试获取生产计数 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/production/count")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取生产计数失败: {e}")
        return False

def test_get_malfunction_stats():
    """测试获取故障统计"""
    print("\n=== 测试获取故障统计 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/malfunction/today")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取故障统计失败: {e}")
        return False

def test_batch_insert():
    """测试批量插入"""
    print("\n=== 测试批量插入生产消息 ===")
    
    # 批量测试数据
    test_data = {
        "messages": [
            {"topic": "factory/line1/counter", "counter": 1},
            {"topic": "factory/line1/counter", "counter": 2},
            {"topic": "factory/line1/counter", "counter": 3}
        ]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/batch/production",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"批量插入失败: {e}")
        return False

def test_with_timestamp():
    """测试带时间戳的插入"""
    print("\n=== 测试带时间戳的插入 ===")
    
    current_time = datetime.now().isoformat()
    
    # 生产消息
    production_data = {
        "topic": "factory/line1/counter",
        "counter": 100,
        "received_at": current_time
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/production/message",
            json=production_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"生产消息 - 状态码: {response.status_code}")
        print(f"生产消息 - 响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"插入带时间戳的生产消息失败: {e}")
    
    # 故障数据
    malfunction_data = {
        "line_number": 1,
        "status": "B",
        "cmd": 1,
        "timestamp": current_time
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/malfunction/raw",
            json=malfunction_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"故障数据 - 状态码: {response.status_code}")
        print(f"故障数据 - 响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"插入带时间戳的故障数据失败: {e}")

def test_error_cases():
    """测试错误情况"""
    print("\n=== 测试错误情况 ===")
    
    # 测试缺少必需字段
    print("1. 测试缺少topic字段:")
    try:
        response = requests.post(
            f"{BASE_URL}/api/production/message",
            json={"counter": 1},
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试无效的故障状态
    print("\n2. 测试无效的故障状态:")
    try:
        response = requests.post(
            f"{BASE_URL}/api/malfunction/raw",
            json={"line_number": 1, "status": "Z", "cmd": 1},
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试不存在的接口
    print("\n3. 测试不存在的接口:")
    try:
        response = requests.get(f"{BASE_URL}/api/nonexistent")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"错误: {e}")

def simulate_production_line():
    """模拟生产线运行"""
    print("\n=== 模拟生产线运行 ===")
    
    print("模拟5个生产计数和2个故障...")
    
    # 模拟生产计数
    for i in range(1, 6):
        production_data = {
            "topic": "factory/line1/counter",
            "counter": i
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/production/message",
                json=production_data,
                headers={'Content-Type': 'application/json'}
            )
            if response.status_code == 200:
                print(f"✓ 生产计数 {i} 插入成功")
            else:
                print(f"✗ 生产计数 {i} 插入失败")
        except Exception as e:
            print(f"✗ 生产计数 {i} 插入错误: {e}")
        
        time.sleep(0.5)  # 模拟时间间隔
    
    # 模拟故障
    malfunctions = [
        {"status": "A", "cmd": 1},  # 假焊故障增加
        {"status": "B", "cmd": 1}   # 连锡故障增加
    ]
    
    for i, mal in enumerate(malfunctions, 1):
        malfunction_data = {
            "line_number": 1,
            "status": mal["status"],
            "cmd": mal["cmd"]
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/malfunction/raw",
                json=malfunction_data,
                headers={'Content-Type': 'application/json'}
            )
            if response.status_code == 200:
                print(f"✓ 故障 {mal['status']} 插入成功")
            else:
                print(f"✗ 故障 {mal['status']} 插入失败")
        except Exception as e:
            print(f"✗ 故障 {mal['status']} 插入错误: {e}")
        
        time.sleep(0.5)

def main():
    """主测试函数"""
    print("开始API测试...")
    
    # 检查服务器是否运行
    if not test_health_check():
        print("服务器未运行，请先启动 flask_api.py")
        return
    
    # 运行各种测试
    test_insert_production_message()
    test_insert_malfunction()
    test_get_production_count()
    test_get_malfunction_stats()
    test_batch_insert()
    test_with_timestamp()
    test_error_cases()
    
    # 模拟生产线运行
    simulate_production_line()
    
    # 最终统计
    print("\n=== 最终统计 ===")
    test_get_production_count()
    test_get_malfunction_stats()
    
    print("\nAPI测试完成！")

if __name__ == "__main__":
    main()
