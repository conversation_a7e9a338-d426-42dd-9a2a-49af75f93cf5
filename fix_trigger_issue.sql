-- ===================================================================
-- 修复触发器问题
-- 解决 "Can't update table in stored function/trigger" 错误
-- ===================================================================

USE production_data;

-- 删除现有的触发器
DROP TRIGGER IF EXISTS `trigger_line1_counter`;
DROP TRIGGER IF EXISTS `trigger_line1_counter_after`;

-- 创建 BEFORE INSERT 触发器来设置 product_id
delimiter ;;
CREATE TRIGGER `trigger_line1_counter` BEFORE INSERT ON `line1_production_message_log` FOR EACH ROW BEGIN
    DECLARE current_product_id VARCHAR(255);
    
    IF NEW.topic = 'factory/line1/counter' THEN
        -- 获取当前生产线的product_id并设置到NEW记录中
        SET current_product_id = get_line_product_id(1);
        SET NEW.product_id = current_product_id;
    END IF;
END
;;
delimiter ;

-- 创建 AFTER INSERT 触发器来处理计数
delimiter ;;
CREATE TRIGGER `trigger_line1_counter_after` AFTER INSERT ON `line1_production_message_log` FOR EACH ROW BEGIN
    IF NEW.topic = 'factory/line1/counter' THEN
        -- 处理计数
        CALL process_line1_counter(NEW.topic, NEW.received_at);
    END IF;
END
;;
delimiter ;

-- 验证触发器创建成功
SHOW TRIGGERS LIKE 'line1_production_message_log';

SELECT 'Trigger fix completed successfully!' as status;
