#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask Web API for Production Data
提供 RESTful API 接口用于插入和查询生产数据
"""

from flask import Flask, request, jsonify
from datetime import datetime
import json
from production_data_api import ProductionDataAPI

app = Flask(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '',  # 请修改为实际密码
    'database': 'production_data'
}

def get_api_instance():
    """获取API实例"""
    api = ProductionDataAPI(**DB_CONFIG)
    if api.connect():
        return api
    return None

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "Production Data API"
    })

@app.route('/api/production/message', methods=['POST'])
def insert_production_message():
    """
    插入生产消息
    
    POST /api/production/message
    {
        "topic": "factory/line1/counter",
        "counter": 1,
        "received_at": "2025-08-23T14:30:00"  // 可选，默认当前时间
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"success": False, "error": "请提供JSON数据"}), 400
        
        topic = data.get('topic')
        if not topic:
            return jsonify({"success": False, "error": "topic字段是必需的"}), 400
        
        counter = data.get('counter')
        received_at_str = data.get('received_at')
        
        # 解析时间
        received_at = None
        if received_at_str:
            try:
                received_at = datetime.fromisoformat(received_at_str.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"success": False, "error": "时间格式错误，请使用ISO格式"}), 400
        
        # 插入数据
        api = get_api_instance()
        if not api:
            return jsonify({"success": False, "error": "数据库连接失败"}), 500
        
        try:
            result = api.insert_production_message(topic, counter, received_at)
            return jsonify(result), 200 if result['success'] else 500
        finally:
            api.disconnect()
            
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/malfunction/raw', methods=['POST'])
def insert_malfunction_raw():
    """
    插入故障原始数据
    
    POST /api/malfunction/raw
    {
        "line_number": 1,
        "status": "A",
        "cmd": 1,
        "timestamp": "2025-08-23T14:30:00"  // 可选，默认当前时间
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"success": False, "error": "请提供JSON数据"}), 400
        
        # 验证必需字段
        line_number = data.get('line_number')
        status = data.get('status')
        cmd = data.get('cmd')
        
        if line_number is None:
            return jsonify({"success": False, "error": "line_number字段是必需的"}), 400
        if not status:
            return jsonify({"success": False, "error": "status字段是必需的"}), 400
        if cmd is None:
            return jsonify({"success": False, "error": "cmd字段是必需的"}), 400
        
        timestamp_str = data.get('timestamp')
        
        # 解析时间
        timestamp = None
        if timestamp_str:
            try:
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"success": False, "error": "时间格式错误，请使用ISO格式"}), 400
        
        # 插入数据
        api = get_api_instance()
        if not api:
            return jsonify({"success": False, "error": "数据库连接失败"}), 500
        
        try:
            result = api.insert_malfunction_raw(line_number, status, cmd, timestamp)
            return jsonify(result), 200 if result['success'] else 500
        finally:
            api.disconnect()
            
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/production/count', methods=['GET'])
def get_production_count():
    """
    获取当前生产计数
    
    GET /api/production/count
    """
    try:
        api = get_api_instance()
        if not api:
            return jsonify({"success": False, "error": "数据库连接失败"}), 500
        
        try:
            result = api.get_current_production_count()
            return jsonify(result), 200 if result['success'] else 500
        finally:
            api.disconnect()
            
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/malfunction/today', methods=['GET'])
def get_today_malfunction():
    """
    获取今日故障统计
    
    GET /api/malfunction/today
    """
    try:
        api = get_api_instance()
        if not api:
            return jsonify({"success": False, "error": "数据库连接失败"}), 500
        
        try:
            result = api.get_today_malfunction_stats()
            return jsonify(result), 200 if result['success'] else 500
        finally:
            api.disconnect()
            
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/batch/production', methods=['POST'])
def batch_insert_production():
    """
    批量插入生产消息
    
    POST /api/batch/production
    {
        "messages": [
            {"topic": "factory/line1/counter", "counter": 1},
            {"topic": "factory/line1/counter", "counter": 2}
        ]
    }
    """
    try:
        data = request.get_json()
        
        if not data or 'messages' not in data:
            return jsonify({"success": False, "error": "请提供messages数组"}), 400
        
        messages = data['messages']
        if not isinstance(messages, list):
            return jsonify({"success": False, "error": "messages必须是数组"}), 400
        
        api = get_api_instance()
        if not api:
            return jsonify({"success": False, "error": "数据库连接失败"}), 500
        
        try:
            results = []
            for msg in messages:
                topic = msg.get('topic')
                counter = msg.get('counter')
                received_at_str = msg.get('received_at')
                
                received_at = None
                if received_at_str:
                    try:
                        received_at = datetime.fromisoformat(received_at_str.replace('Z', '+00:00'))
                    except ValueError:
                        results.append({"success": False, "error": "时间格式错误"})
                        continue
                
                result = api.insert_production_message(topic, counter, received_at)
                results.append(result)
            
            return jsonify({
                "success": True,
                "total": len(messages),
                "results": results
            })
        finally:
            api.disconnect()
            
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({"success": False, "error": "接口不存在"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"success": False, "error": "服务器内部错误"}), 500

if __name__ == '__main__':
    print("=== Production Data API Server ===")
    print("API接口说明:")
    print("POST /api/production/message - 插入生产消息")
    print("POST /api/malfunction/raw - 插入故障数据")
    print("GET  /api/production/count - 获取生产计数")
    print("GET  /api/malfunction/today - 获取今日故障统计")
    print("POST /api/batch/production - 批量插入生产消息")
    print("GET  /health - 健康检查")
    print("=====================================")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
