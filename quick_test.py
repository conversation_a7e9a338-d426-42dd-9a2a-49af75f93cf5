#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
测试两个API接口
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_production_message():
    """测试插入生产消息"""
    print("=== 测试生产消息 ===")
    
    data = {
        "topic": "factory/line1/counter",
        "counter": 1
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/production/message", json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_malfunction_data():
    """测试插入故障数据"""
    print("\n=== 测试故障数据 ===")
    
    data = {
        "line_number": 1,
        "status": "A",
        "cmd": 1
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/malfunction/raw", json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_batch():
    """批量测试"""
    print("\n=== 批量测试 ===")
    
    # 批量插入生产消息
    print("1. 批量插入生产消息:")
    for i in range(1, 4):
        data = {"topic": "factory/line1/counter", "counter": i}
        try:
            response = requests.post(f"{BASE_URL}/api/production/message", json=data)
            if response.status_code == 200:
                print(f"   ✓ 生产计数 {i} 成功")
            else:
                print(f"   ✗ 生产计数 {i} 失败")
        except Exception as e:
            print(f"   ✗ 生产计数 {i} 错误: {e}")
    
    # 批量插入故障数据
    print("\n2. 批量插入故障数据:")
    malfunctions = [
        {"status": "A", "name": "假焊"},
        {"status": "B", "name": "连锡"},
        {"status": "C", "name": "错料"}
    ]
    
    for mal in malfunctions:
        data = {"line_number": 1, "status": mal["status"], "cmd": 1}
        try:
            response = requests.post(f"{BASE_URL}/api/malfunction/raw", json=data)
            if response.status_code == 200:
                print(f"   ✓ {mal['name']}({mal['status']}) 成功")
            else:
                print(f"   ✗ {mal['name']}({mal['status']}) 失败")
        except Exception as e:
            print(f"   ✗ {mal['name']}({mal['status']}) 错误: {e}")

def main():
    """主函数"""
    print("开始API测试...")
    print(f"服务器地址: {BASE_URL}")
    
    test_production_message()
    test_malfunction_data()
    test_batch()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
