-- ===================================================================
-- Line2 完整安装脚本
-- 创建 line2 的所有表、视图、存储过程
-- ===================================================================

USE production_data;

-- 执行 line2 表创建
SOURCE line2_tables.sql;

-- 执行 line2 视图创建
SOURCE line2_views.sql;

-- 执行 line2 存储过程创建
SOURCE line2_procedures.sql;

-- 验证安装结果
SELECT 'Checking Line2 installation...' as status;

-- 检查表是否创建成功
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    CREATE_TIME
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'production_data' 
AND TABLE_NAME LIKE 'line2_%'
ORDER BY TABLE_NAME;

-- 检查视图是否创建成功
SELECT 
    TABLE_NAME as VIEW_NAME,
    VIEW_DEFINITION
FROM INFORMATION_SCHEMA.VIEWS 
WHERE TABLE_SCHEMA = 'production_data' 
AND TABLE_NAME LIKE '%line2%'
ORDER BY TABLE_NAME;

-- 检查存储过程是否创建成功
SELECT 
    ROUTINE_NAME,
    ROUTINE_TYPE,
    CREATED
FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_SCHEMA = 'production_data' 
AND ROUTINE_NAME LIKE '%line2%'
ORDER BY ROUTINE_NAME;

-- 检查生产线配置
SELECT 
    line_id,
    line_number,
    product_id,
    created_at,
    updated_at
FROM line1_production_lines
ORDER BY line_number;

SELECT 'Line2 installation completed successfully!' as final_status;
