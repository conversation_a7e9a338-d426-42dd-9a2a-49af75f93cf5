# 产品ID关联修改说明

## 概述
本次修改确保所有 malfunction 和 production 数据都能正确关联到 `line1_production_lines` 表的 `product_id`。

## 主要修改内容

### 1. 数据库表结构修改

#### 唯一索引更新
- `line1_malfunction_daily_count`: 将唯一索引从 `(malfunction_type, date_timestamp)` 改为 `(product_id, malfunction_type, date_timestamp)`
- `line1_malfunction_hourly_count`: 将唯一索引从 `(malfunction_type, hour_timestamp)` 改为 `(product_id, malfunction_type, hour_timestamp)`
- `line1_production_count`: 添加唯一索引 `(product_id)`
- `line1_production_daily_count`: 添加唯一索引 `(product_id, date_timestamp)`
- `line1_production_hourly_count`: 添加唯一索引 `(product_id, hour_timestamp)`

### 2. 存储过程修改

#### `process_line1_counter`
- 修改为从 `line1_production_lines` 表获取当前生产线的 `product_id`
- 在所有插入/更新操作中包含 `product_id`
- 确保按 `product_id` 分组统计数据

#### `increment_malfunction_count`
- 添加获取当前生产线 `product_id` 的逻辑
- 在插入 malfunction 统计数据时包含 `product_id`

#### `process_malfunction_from_raw`
- 修改为从原始数据中获取生产线编号
- 根据生产线编号查找对应的 `product_id`
- 在统计数据中包含正确的 `product_id`

#### 新增 `insert_line1_malfunction_raw`
- 替代原有的 `insert_malfunction_raw` 存储过程
- 专门处理 line1 的 malfunction 原始数据插入
- 自动关联正确的 `product_id`

### 3. 新增函数

#### `get_line_product_id(line_num INT)`
- 根据生产线编号获取对应的 `product_id`
- 返回类型: `VARCHAR(255)`
- 用于其他存储过程和触发器中获取 `product_id`

### 4. 触发器修改

#### `trigger_line1_counter`
- 在处理消息时自动设置 `product_id`
- 确保消息日志记录包含正确的产品关联

## 使用方法

### 1. 应用数据库结构修改
```sql
-- 执行修改后的 production_data.sql 文件
SOURCE production_data.sql;
```

### 2. 更新现有数据
```sql
-- 执行数据更新脚本
SOURCE update_product_id_associations.sql;
```

### 3. 插入新的 malfunction 数据
```sql
-- 使用新的存储过程插入 malfunction 原始数据
CALL insert_line1_malfunction_raw(1, 'A', 1, NOW());
```

### 4. 验证数据关联
```sql
-- 检查数据是否正确关联
SELECT 
    pl.line_number,
    pl.product_id,
    COUNT(mdc.id) as daily_malfunction_records,
    COUNT(pdc.id) as daily_production_records
FROM line1_production_lines pl
LEFT JOIN line1_malfunction_daily_count mdc ON pl.product_id = mdc.product_id
LEFT JOIN line1_production_daily_count pdc ON pl.product_id = pdc.product_id
GROUP BY pl.line_number, pl.product_id;
```

## 数据一致性保证

1. **唯一约束**: 通过修改唯一索引确保同一产品的同一时间段内不会有重复记录
2. **外键关联**: 所有数据表都通过 `product_id` 与 `line1_production_lines` 表关联
3. **自动关联**: 触发器和存储过程自动处理 `product_id` 的设置
4. **数据验证**: 提供验证脚本确保数据正确关联

## 注意事项

1. 在执行修改前请备份现有数据
2. 确保 `line1_production_lines` 表中有对应生产线的记录
3. 新的唯一索引可能会影响现有的重复数据，需要先清理
4. 所有新的数据插入都应该使用修改后的存储过程

## 影响范围

- **生产数据统计**: 现在按产品ID分组统计
- **故障数据统计**: 现在按产品ID分组统计  
- **报表查询**: 需要考虑产品ID维度
- **数据分析**: 可以按产品进行更精确的分析
