# Line2 安装和使用指南

## 概述
基于 line1 的结构，为生产线2创建完整的数据库表、视图、存储过程和API支持。

## 文件说明

### 数据库文件
- `line2_tables.sql` - Line2 数据表和基础存储过程
- `line2_views.sql` - Line2 相关视图
- `line2_procedures.sql` - Line2 专用存储过程
- `install_line2.sql` - 完整安装脚本

### API 文件
- `line2_api_extension.py` - Line2 API 扩展类

## 安装步骤

### 1. 数据库安装
```sql
-- 执行完整安装脚本
SOURCE install_line2.sql;
```

或者分步安装：
```sql
-- 1. 创建表和基础存储过程
SOURCE line2_tables.sql;

-- 2. 创建视图
SOURCE line2_views.sql;

-- 3. 创建专用存储过程
SOURCE line2_procedures.sql;
```

### 2. 验证安装
```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'line2_%';

-- 检查生产线配置
SELECT * FROM line1_production_lines WHERE line_number = 2;

-- 检查视图
SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_production_data LIKE '%line2%';

-- 检查存储过程
SHOW PROCEDURE STATUS WHERE Name LIKE '%line2%';
```

## 数据库结构

### 表结构
- `line2_malfunction_daily_count` - 每日故障统计
- `line2_malfunction_hourly_count` - 每小时故障统计
- `line2_malfunction_raw_data` - 故障原始数据
- `line2_malfunction_type_info` - 故障类型信息
- `line2_production_count` - 生产总计数
- `line2_production_daily_count` - 每日生产统计
- `line2_production_hourly_count` - 每小时生产统计
- `line2_production_message_log` - 生产消息日志

### 视图
- `view_line2_current_count` - 当前生产计数
- `view_line2_recent_daily` - 最近7天生产统计
- `view_line2_today_hourly` - 今日小时生产统计
- `view_line2_malfunction_ranking` - 故障排名
- `view_line2_today_malfunction` - 今日故障统计
- `view_line2_today_hourly_malfunction` - 今日小时故障统计
- `view_line2_current_hour_malfunction` - 当前小时故障统计

### 存储过程
- `process_line2_counter` - 处理生产计数
- `insert_line2_malfunction_raw` - 插入故障原始数据
- `process_line2_malfunction_from_raw` - 处理故障统计
- `get_line2_today_malfunction_stats` - 获取今日故障统计
- `increment_line2_malfunction_count` - 增减故障计数
- `get_line2_production_stats` - 获取生产统计

## API 使用

### Python API 示例
```python
from line2_api_extension import Line2API

# 创建 Line2 API 实例
api = Line2API(
    host='localhost',
    user='root',
    password='your_password',
    database='production_data'
)

# 连接数据库
if api.connect():
    # 插入生产消息
    result = api.insert_line2_production_message('factory/line2/counter', 1)
    print(result)
    
    # 插入故障数据
    result = api.insert_line2_malfunction_raw(2, 'A', 1)
    print(result)
    
    # 获取统计数据
    result = api.get_line2_production_count()
    print(result)
    
    api.disconnect()
```

### 扩展现有 Flask API
如果要在现有的 `minimal_api.py` 中添加 line2 支持，可以添加以下接口：

```python
@app.route('/api/line2/production/message', methods=['POST'])
def insert_line2_production_message():
    # 类似 line1 的实现，但使用 line2 相关的表和存储过程
    pass

@app.route('/api/line2/malfunction/raw', methods=['POST'])
def insert_line2_malfunction_raw():
    # 类似 line1 的实现，但使用 line2 相关的表和存储过程
    pass
```

## 数据关联

### Product ID 配置
Line2 默认配置：
- 生产线编号：2
- 产品ID：300-596-204

可以通过以下 SQL 修改：
```sql
UPDATE line1_production_lines 
SET product_id = 'your_product_id' 
WHERE line_number = 2;
```

### 数据流程
1. **生产消息**：`factory/line2/counter` → `line2_production_message_log` → 触发统计更新
2. **故障数据**：原始数据 → `line2_malfunction_raw_data` → 自动统计处理

## 测试验证

### 1. 基础功能测试
```python
python line2_api_extension.py
```

### 2. 手动数据插入测试
```sql
-- 插入生产消息
INSERT INTO line2_production_message_log (topic, counter, received_at) 
VALUES ('factory/line2/counter', 1, NOW());

-- 调用生产计数处理
CALL process_line2_counter('factory/line2/counter', NOW());

-- 插入故障数据
CALL insert_line2_malfunction_raw(2, 'A', 1, NOW());
```

### 3. 查看统计结果
```sql
-- 查看生产统计
SELECT * FROM view_line2_current_count;
SELECT * FROM view_line2_today_hourly;

-- 查看故障统计
SELECT * FROM view_line2_today_malfunction;
SELECT * FROM view_line2_malfunction_ranking;
```

## 注意事项

1. **生产线编号**：Line2 的所有操作都使用生产线编号 2
2. **Product ID**：自动从 `line1_production_lines` 表获取
3. **数据隔离**：Line1 和 Line2 的数据完全分离，使用不同的表
4. **存储过程**：每个生产线都有独立的存储过程
5. **API 扩展**：可以基于现有 API 结构轻松扩展

## 故障排除

### 常见问题
1. **表不存在**：确保执行了完整的安装脚本
2. **Product ID 为空**：检查 `line1_production_lines` 表中是否有 line_number=2 的记录
3. **存储过程错误**：检查存储过程是否正确创建

### 检查命令
```sql
-- 检查安装状态
SELECT 'Tables' as type, COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'production_data' AND TABLE_NAME LIKE 'line2_%'
UNION ALL
SELECT 'Views', COUNT(*) FROM INFORMATION_SCHEMA.VIEWS 
WHERE TABLE_SCHEMA = 'production_data' AND TABLE_NAME LIKE '%line2%'
UNION ALL
SELECT 'Procedures', COUNT(*) FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_SCHEMA = 'production_data' AND ROUTINE_NAME LIKE '%line2%';
```

## 扩展到更多生产线

基于这个模式，可以轻松扩展到 line3, line4 等：
1. 复制相关 SQL 文件
2. 替换所有 `line2` 为 `line3`
3. 修改生产线编号和产品ID
4. 创建对应的 API 扩展类
