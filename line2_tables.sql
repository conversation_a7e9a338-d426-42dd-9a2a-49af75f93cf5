/*
 Line2 Production Data Tables
 基于 line1 结构创建 line2 相关表
 
 Date: 2025-08-23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for line2_malfunction_daily_count
-- ----------------------------
DROP TABLE IF EXISTS `line2_malfunction_daily_count`;
CREATE TABLE `line2_malfunction_daily_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `malfunction_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '坏机类型 A-J',
  `count` int(11) NOT NULL DEFAULT 0 COMMENT '坏机次数',
  `date_timestamp` date NOT NULL COMMENT '统计日期',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_product_type_date`(`product_id`, `malfunction_type`, `date_timestamp`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for line2_malfunction_hourly_count
-- ----------------------------
DROP TABLE IF EXISTS `line2_malfunction_hourly_count`;
CREATE TABLE `line2_malfunction_hourly_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `malfunction_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '坏机类型 A-J',
  `count` int(11) NOT NULL DEFAULT 0 COMMENT '坏机次数',
  `hour_timestamp` datetime NOT NULL COMMENT '小时时间戳 (YYYY-MM-DD HH:00:00)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_product_type_hour`(`product_id`, `malfunction_type`, `hour_timestamp`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for line2_malfunction_raw_data
-- ----------------------------
DROP TABLE IF EXISTS `line2_malfunction_raw_data`;
CREATE TABLE `line2_malfunction_raw_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `line` int(11) NOT NULL COMMENT '生产线编号',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '故障状态类型 A-J',
  `cmd` tinyint(4) NOT NULL COMMENT '操作命令：1=增加，0=减少',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
  `processed` tinyint(1) NULL DEFAULT 0 COMMENT '是否已处理到统计表',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_line`(`line`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_timestamp`(`timestamp`) USING BTREE,
  INDEX `idx_processed`(`processed`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for line2_malfunction_type_info
-- ----------------------------
DROP TABLE IF EXISTS `line2_malfunction_type_info`;
CREATE TABLE `line2_malfunction_type_info`  (
  `type_code` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '坏机类型代码 A-J',
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '坏机类型名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '类型描述',
  PRIMARY KEY (`type_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of line2_malfunction_type_info
-- ----------------------------
INSERT INTO `line2_malfunction_type_info` VALUES ('A', '', '假焊', NULL);
INSERT INTO `line2_malfunction_type_info` VALUES ('B', '', '连锡', NULL);
INSERT INTO `line2_malfunction_type_info` VALUES ('C', '', '错料', NULL);
INSERT INTO `line2_malfunction_type_info` VALUES ('D', NULL, '漏料', NULL);
INSERT INTO `line2_malfunction_type_info` VALUES ('E', NULL, '反装', NULL);
INSERT INTO `line2_malfunction_type_info` VALUES ('F', NULL, '坏料', NULL);
INSERT INTO `line2_malfunction_type_info` VALUES ('G', NULL, '功能坏', NULL);
INSERT INTO `line2_malfunction_type_info` VALUES ('H', NULL, '开路', NULL);
INSERT INTO `line2_malfunction_type_info` VALUES ('I', NULL, '短路', NULL);
INSERT INTO `line2_malfunction_type_info` VALUES ('J', NULL, '其他', NULL);

-- ----------------------------
-- Table structure for line2_production_count
-- ----------------------------
DROP TABLE IF EXISTS `line2_production_count`;
CREATE TABLE `line2_production_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `total_count` int(11) NOT NULL DEFAULT 0,
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for line2_production_daily_count
-- ----------------------------
DROP TABLE IF EXISTS `line2_production_daily_count`;
CREATE TABLE `line2_production_daily_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `date_timestamp` date NOT NULL,
  `count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_product_date`(`product_id`, `date_timestamp`) USING BTREE,
  INDEX `idx_line2_daily_timestamp`(`date_timestamp`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for line2_production_hourly_count
-- ----------------------------
DROP TABLE IF EXISTS `line2_production_hourly_count`;
CREATE TABLE `line2_production_hourly_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `hour_timestamp` datetime NOT NULL,
  `count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_product_hour`(`product_id`, `hour_timestamp`) USING BTREE,
  INDEX `idx_line2_hourly_timestamp`(`hour_timestamp`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for line2_production_message_log
-- ----------------------------
DROP TABLE IF EXISTS `line2_production_message_log`;
CREATE TABLE `line2_production_message_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `topic` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `qos` int(11) NULL DEFAULT 0,
  `counter` int(11) NULL DEFAULT NULL,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `received_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_line2_message_topic`(`topic`) USING BTREE,
  INDEX `idx_line2_message_timestamp`(`received_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 添加 line2 到生产线配置表
INSERT INTO `line1_production_lines` VALUES (2, 2, '300-596-204', NOW(), NOW())
ON DUPLICATE KEY UPDATE
    product_id = '300-596-204',
    updated_at = NOW();

-- ----------------------------
-- Procedure structure for process_line2_counter
-- ----------------------------
DROP PROCEDURE IF EXISTS `process_line2_counter`;
delimiter ;;
CREATE PROCEDURE `process_line2_counter`(IN msg_topic VARCHAR(100), IN msg_time TIMESTAMP)
BEGIN
    DECLARE current_hour DATETIME;
    DECLARE current_day DATE;
    DECLARE hour_count INT;
    DECLARE day_count INT;
    DECLARE current_product_id VARCHAR(255);

    -- 只处理line2/counter消息
    IF msg_topic = 'factory/line2/counter' THEN
        -- 获取当前生产线的product_id
        SELECT product_id INTO current_product_id
        FROM line1_production_lines
        WHERE line_number = 2
        LIMIT 1;

        -- 更新总计数，包含product_id
        INSERT INTO line2_production_count (product_id, total_count, last_updated)
        VALUES (current_product_id, 1, msg_time)
        ON DUPLICATE KEY UPDATE
            total_count = total_count + 1,
            last_updated = msg_time;

        -- 获取当前小时和日期
        SET current_hour = DATE_FORMAT(msg_time, '%Y-%m-%d %H:00:00');
        SET current_day = DATE(msg_time);

        -- 检查小时记录是否存在
        SELECT COUNT(*) INTO hour_count FROM line2_production_hourly_count
        WHERE hour_timestamp = current_hour AND product_id = current_product_id;

        -- 更新或插入小时计数，包含product_id
        IF hour_count > 0 THEN
            UPDATE line2_production_hourly_count
            SET count = count + 1
            WHERE hour_timestamp = current_hour AND product_id = current_product_id;
        ELSE
            INSERT INTO line2_production_hourly_count (product_id, hour_timestamp, count)
            VALUES (current_product_id, current_hour, 1);
        END IF;

        -- 检查日记录是否存在
        SELECT COUNT(*) INTO day_count FROM line2_production_daily_count
        WHERE date_timestamp = current_day AND product_id = current_product_id;

        -- 更新或插入日计数，包含product_id
        IF day_count > 0 THEN
            UPDATE line2_production_daily_count
            SET count = count + 1
            WHERE date_timestamp = current_day AND product_id = current_product_id;
        ELSE
            INSERT INTO line2_production_daily_count (product_id, date_timestamp, count)
            VALUES (current_product_id, current_day, 1);
        END IF;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for insert_line2_malfunction_raw
-- ----------------------------
DROP PROCEDURE IF EXISTS `insert_line2_malfunction_raw`;
delimiter ;;
CREATE PROCEDURE `insert_line2_malfunction_raw`(
    IN line_num INT,
    IN mal_status CHAR(1),
    IN mal_cmd TINYINT,
    IN mal_timestamp TIMESTAMP)
BEGIN
    DECLARE new_id INT;
    DECLARE current_product_id VARCHAR(255);

    -- 获取对应生产线的product_id
    SET current_product_id = get_line_product_id(line_num);

    -- 插入原始数据，包含product_id
    INSERT INTO line2_malfunction_raw_data
    (product_id, line, status, cmd, timestamp)
    VALUES (current_product_id, line_num, mal_status, mal_cmd, mal_timestamp);

    -- 获取新插入的ID
    SET new_id = LAST_INSERT_ID();

    -- 自动处理统计
    CALL process_line2_malfunction_from_raw(new_id);
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for process_line2_malfunction_from_raw
-- ----------------------------
DROP PROCEDURE IF EXISTS `process_line2_malfunction_from_raw`;
delimiter ;;
CREATE PROCEDURE `process_line2_malfunction_from_raw`(IN raw_id INT)
BEGIN
    DECLARE mal_type CHAR(1);
    DECLARE mal_timestamp TIMESTAMP;
    DECLARE target_date DATE;
    DECLARE target_hour DATETIME;
    DECLARE is_processed BOOLEAN DEFAULT FALSE;
    DECLARE current_product_id VARCHAR(255);
    DECLARE line_num INT;
    DECLARE mal_cmd TINYINT;
    DECLARE count_change INT;

    -- 获取原始数据，包含cmd字段
    SELECT status, timestamp, processed, line, cmd
    INTO mal_type, mal_timestamp, is_processed, line_num, mal_cmd
    FROM line2_malfunction_raw_data
    WHERE id = raw_id;

    -- 如果数据存在且未处理过
    IF mal_type IS NOT NULL AND NOT is_processed THEN
        -- 获取对应生产线的product_id
        SELECT product_id INTO current_product_id
        FROM line1_production_lines
        WHERE line_number = line_num
        LIMIT 1;

        -- 检查类型是否有效（A-J）
        IF mal_type REGEXP '^[A-J]$' THEN
            SET target_date = DATE(mal_timestamp);
            SET target_hour = DATE_FORMAT(mal_timestamp, '%Y-%m-%d %H:00:00');

            -- 根据cmd确定计数变化：1=增加，0=减少
            SET count_change = IF(mal_cmd = 1, 1, -1);

            -- 更新每日计数，包含product_id
            INSERT INTO line2_malfunction_daily_count (product_id, malfunction_type, count, date_timestamp)
            VALUES (current_product_id, mal_type, count_change, target_date)
            ON DUPLICATE KEY UPDATE
                count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
                updated_at = CURRENT_TIMESTAMP;

            -- 更新每小时计数，包含product_id
            INSERT INTO line2_malfunction_hourly_count (product_id, malfunction_type, count, hour_timestamp)
            VALUES (current_product_id, mal_type, count_change, target_hour)
            ON DUPLICATE KEY UPDATE
                count = GREATEST(0, count + count_change),  -- 确保计数不会小于0
                updated_at = CURRENT_TIMESTAMP;

            -- 标记为已处理
            UPDATE line2_malfunction_raw_data
            SET processed = TRUE
            WHERE id = raw_id;
        END IF;
    END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
