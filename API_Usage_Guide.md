# 生产数据API使用指南

## 概述
这套Python API提供了用于插入和查询生产数据的完整接口，包括生产计数和故障数据的管理。

## 文件说明

### 核心文件
- `production_data_api.py` - 核心数据库操作类
- `flask_api.py` - Flask Web API服务器
- `test_api.py` - API测试脚本
- `requirements.txt` - Python依赖包

## 安装和配置

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 数据库配置
修改 `flask_api.py` 中的数据库配置：
```python
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'your_password',  # 修改为实际密码
    'database': 'production_data'
}
```

### 3. 启动API服务器
```bash
python flask_api.py
```
服务器将在 `http://localhost:5000` 启动

## API接口说明

### 1. 健康检查
```
GET /health
```
**响应示例:**
```json
{
  "status": "healthy",
  "timestamp": "2025-08-23T14:30:00",
  "service": "Production Data API"
}
```

### 2. 插入生产消息
```
POST /api/production/message
```
**请求体:**
```json
{
  "topic": "factory/line1/counter",
  "counter": 1,
  "received_at": "2025-08-23T14:30:00"  // 可选
}
```
**响应示例:**
```json
{
  "success": true,
  "message_id": 123,
  "topic": "factory/line1/counter",
  "counter": 1,
  "received_at": "2025-08-23T14:30:00"
}
```

### 3. 插入故障数据
```
POST /api/malfunction/raw
```
**请求体:**
```json
{
  "line_number": 1,
  "status": "A",  // A-J 故障类型
  "cmd": 1,       // 1=增加, 0=减少
  "timestamp": "2025-08-23T14:30:00"  // 可选
}
```
**响应示例:**
```json
{
  "success": true,
  "malfunction_id": 456,
  "line_number": 1,
  "status": "A",
  "cmd": 1,
  "timestamp": "2025-08-23T14:30:00"
}
```

### 4. 获取生产计数
```
GET /api/production/count
```
**响应示例:**
```json
{
  "success": true,
  "data": {
    "total_count": 1500,
    "last_updated": "2025-08-23T14:30:00"
  }
}
```

### 5. 获取今日故障统计
```
GET /api/malfunction/today
```
**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "malfunction_type": "A",
      "type_name": "假焊",
      "count": 5
    },
    {
      "malfunction_type": "B", 
      "type_name": "连锡",
      "count": 3
    }
  ]
}
```

### 6. 批量插入生产消息
```
POST /api/batch/production
```
**请求体:**
```json
{
  "messages": [
    {"topic": "factory/line1/counter", "counter": 1},
    {"topic": "factory/line1/counter", "counter": 2},
    {"topic": "factory/line1/counter", "counter": 3}
  ]
}
```

## 故障类型说明
- A: 假焊
- B: 连锡  
- C: 错料
- D: 漏料
- E: 反装
- F: 坏料
- G: 功能坏
- H: 开路
- I: 短路
- J: 其他

## 使用示例

### Python 直接调用
```python
from production_data_api import ProductionDataAPI

# 创建API实例
api = ProductionDataAPI(
    host='localhost',
    user='root', 
    password='your_password',
    database='production_data'
)

# 连接数据库
if api.connect():
    # 插入生产消息
    result = api.insert_production_message('factory/line1/counter', 1)
    print(result)
    
    # 插入故障数据
    result = api.insert_malfunction_raw(1, 'A', 1)
    print(result)
    
    # 关闭连接
    api.disconnect()
```

### HTTP 请求示例
```python
import requests

# 插入生产消息
response = requests.post(
    'http://localhost:5000/api/production/message',
    json={'topic': 'factory/line1/counter', 'counter': 1}
)
print(response.json())

# 插入故障数据
response = requests.post(
    'http://localhost:5000/api/malfunction/raw',
    json={'line_number': 1, 'status': 'A', 'cmd': 1}
)
print(response.json())
```

### curl 示例
```bash
# 插入生产消息
curl -X POST http://localhost:5000/api/production/message \
  -H "Content-Type: application/json" \
  -d '{"topic": "factory/line1/counter", "counter": 1}'

# 插入故障数据
curl -X POST http://localhost:5000/api/malfunction/raw \
  -H "Content-Type: application/json" \
  -d '{"line_number": 1, "status": "A", "cmd": 1}'

# 获取生产计数
curl http://localhost:5000/api/production/count

# 获取故障统计
curl http://localhost:5000/api/malfunction/today
```

## 测试

### 运行测试脚本
```bash
# 确保API服务器正在运行
python flask_api.py

# 在另一个终端运行测试
python test_api.py
```

测试脚本将执行以下测试：
- 健康检查
- 插入生产消息
- 插入故障数据
- 获取统计数据
- 批量插入
- 错误情况处理
- 模拟生产线运行

## 错误处理

API会返回标准的HTTP状态码：
- 200: 成功
- 400: 请求参数错误
- 404: 接口不存在
- 500: 服务器内部错误

错误响应格式：
```json
{
  "success": false,
  "error": "错误描述"
}
```

## 注意事项

1. **数据库连接**: 确保MySQL服务正在运行且配置正确
2. **时间格式**: 使用ISO 8601格式 (YYYY-MM-DDTHH:MM:SS)
3. **故障类型**: 只接受A-J范围内的字符
4. **操作命令**: cmd字段只接受0或1
5. **产品关联**: 所有数据会自动关联到对应生产线的product_id

## 扩展功能

可以根据需要添加更多接口：
- 历史数据查询
- 数据统计分析
- 实时数据推送
- 数据导出功能
